from django.urls import path
from . import views

urlpatterns = [
    # Cached metrics endpoints
    path('api/metrics/', views.CachedMetricsView.as_view(), name='cached-metrics'),
    path('api/metrics/cache-status/', views.CacheStatusView.as_view(), name='cache-status'),
    
    # Cached list endpoints with pagination
    path('api/cached/companies/', views.CachedCompanyListView.as_view(), name='cached-companies'),
    path('api/cached/students/', views.CachedStudentListView.as_view(), name='cached-students'),
    path('api/cached/jobs/', views.CachedJobListView.as_view(), name='cached-jobs'),
]
