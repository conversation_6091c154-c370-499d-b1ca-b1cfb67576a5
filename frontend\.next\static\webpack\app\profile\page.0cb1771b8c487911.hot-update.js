"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./src/api/students.js":
/*!*****************************!*\
  !*** ./src/api/students.js ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   studentsAPI: () => (/* binding */ studentsAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"(app-pages-browser)/./src/api/auth.js\");\n\n\n// Set the base URL for all API requests\nconst API_BASE_URL = 'http://localhost:8000';\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Add request interceptor to include auth token\napi.interceptors.request.use((config)=>{\n    const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n    if (token) {\n        config.headers.Authorization = \"Bearer \".concat(token);\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Add response interceptor for error handling\napi.interceptors.response.use((response)=>response, (error)=>{\n    var _error_response;\n    if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n        // Token expired or invalid\n        localStorage.removeItem('access_token');\n        localStorage.removeItem('refresh_token');\n        window.location.href = '/login';\n    }\n    return Promise.reject(error);\n});\nconst studentsAPI = {\n    // Get all students\n    getStudents: async function() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const response = await api.get('/api/accounts/students/', {\n            params\n        });\n        return response.data;\n    },\n    // Get single student\n    getStudent: async (id)=>{\n        const response = await api.get(\"/api/accounts/students/\".concat(id, \"/\"));\n        return response.data;\n    },\n    // Update student\n    updateStudent: async (id, data)=>{\n        console.log('updateStudent called with:', {\n            id,\n            data\n        });\n        // Check authentication\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        console.log('Auth token available:', !!token);\n        if (token) {\n            console.log('Token preview:', token.substring(0, 20) + '...');\n        }\n        if (!token) {\n            throw new Error('Authentication required to update student');\n        }\n        // Clean data to ensure proper format\n        const cleanedData = {\n            ...data\n        };\n        // Ensure numeric fields are properly formatted\n        [\n            'joining_year',\n            'passout_year'\n        ].forEach((field)=>{\n            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\n                const num = parseInt(cleanedData[field]);\n                cleanedData[field] = isNaN(num) ? null : num;\n            }\n        });\n        // Ensure string fields are properly formatted\n        const stringFields = [\n            'first_name',\n            'last_name',\n            'student_id',\n            'contact_email',\n            'phone',\n            'branch',\n            'gpa',\n            'date_of_birth',\n            'address',\n            'city',\n            'district',\n            'state',\n            'pincode',\n            'country',\n            'parent_contact',\n            'education',\n            'skills',\n            'tenth_cgpa',\n            'tenth_percentage',\n            'tenth_board',\n            'tenth_school',\n            'tenth_year_of_passing',\n            'tenth_location',\n            'tenth_specialization',\n            'twelfth_cgpa',\n            'twelfth_percentage',\n            'twelfth_board',\n            'twelfth_school',\n            'twelfth_year_of_passing',\n            'twelfth_location',\n            'twelfth_specialization'\n        ];\n        stringFields.forEach((field)=>{\n            if (cleanedData[field] !== null && cleanedData[field] !== undefined) {\n                cleanedData[field] = String(cleanedData[field]).trim();\n            }\n        });\n        // Remove undefined values\n        Object.keys(cleanedData).forEach((key)=>{\n            if (cleanedData[key] === undefined) {\n                delete cleanedData[key];\n            }\n        });\n        console.log('Cleaned data being sent:', cleanedData);\n        // Try the ViewSet endpoint first (more RESTful)\n        try {\n            console.log('Trying ViewSet endpoint:', \"/api/accounts/profiles/\".concat(id, \"/\"));\n            const response = await api.patch(\"/api/accounts/profiles/\".concat(id, \"/\"), cleanedData);\n            console.log('ViewSet endpoint success:', response.data);\n            return response.data;\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response3;\n            console.error('ViewSet endpoint failed:', {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                statusText: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.statusText,\n                data: (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.data,\n                headers: (_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.headers,\n                config: error.config\n            });\n            // If ViewSet fails, try the fallback endpoint\n            try {\n                console.log('Trying fallback endpoint:', \"/api/accounts/students/\".concat(id, \"/update/\"));\n                const response = await api.patch(\"/api/accounts/students/\".concat(id, \"/update/\"), cleanedData);\n                console.log('Fallback endpoint success:', response.data);\n                return response.data;\n            } catch (updateError) {\n                var _error_response4, _error_response5, _updateError_response, _updateError_response1, _updateError_response2;\n                console.error('Failed to update student via both endpoints:', {\n                    viewSetError: {\n                        status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                        data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n                    },\n                    updateViewError: {\n                        status: (_updateError_response = updateError.response) === null || _updateError_response === void 0 ? void 0 : _updateError_response.status,\n                        data: (_updateError_response1 = updateError.response) === null || _updateError_response1 === void 0 ? void 0 : _updateError_response1.data\n                    }\n                });\n                // Throw the more specific error\n                const primaryError = ((_updateError_response2 = updateError.response) === null || _updateError_response2 === void 0 ? void 0 : _updateError_response2.status) === 400 ? updateError : error;\n                throw primaryError;\n            }\n        }\n    },\n    // Get current user profile\n    getProfile: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.get('/api/auth/profile/', {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Update profile information\n    updateProfile: async (data)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.patch('/api/auth/profile/', data, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload profile image\n    uploadProfileImage: async (file)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('image', file);\n        return api.post('/api/accounts/profiles/me/upload_profile_image/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload resume\n    uploadResume: async (file)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('resume', file);\n        return api.patch('/api/auth/profile/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Get all resumes for the student\n    getResumes: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        if (!token) {\n            throw new Error('Authentication required to fetch resumes');\n        }\n        try {\n            const response = await api.get('/api/accounts/profiles/me/resumes/', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            // Ensure we're getting a proper response\n            if (!response.data) {\n                console.error('Empty response when fetching resumes');\n                return [];\n            }\n            // Handle different response formats\n            if (Array.isArray(response.data)) {\n                return response.data;\n            } else if (response.data.data && Array.isArray(response.data.data)) {\n                return response.data.data;\n            } else {\n                console.error('Unexpected resume data format:', response.data);\n                return [];\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Resume fetch error:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            throw error;\n        }\n    },\n    // Delete a specific resume\n    deleteResume: async (resumeId)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        try {\n            console.log(\"Attempting to delete resume with ID: \".concat(resumeId));\n            let success = false;\n            // Attempt different deletion strategies\n            const strategies = [\n                // Strategy 1: Standard DELETE request\n                async ()=>{\n                    try {\n                        const response = await api.delete(\"/api/accounts/profiles/me/resumes/\".concat(resumeId, \"/\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('DELETE resume successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 1 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 2: POST to remove endpoint\n                async ()=>{\n                    try {\n                        const response = await api.post(\"/api/accounts/profiles/me/resumes/\".concat(resumeId, \"/remove/\"), {}, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('POST remove successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 2 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 3: Patch profile with delete_resume field\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            delete_resume: resumeId\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('PATCH profile successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 3 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 4: Reset all resumes (extreme fallback)\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            reset_resumes: true\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Reset resumes successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 4 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                }\n            ];\n            // Try each strategy in sequence until one succeeds\n            for (const strategy of strategies){\n                const result = await strategy();\n                if (result.success) {\n                    success = true;\n                    break;\n                }\n            }\n            // Clear any locally cached data for this resume regardless of backend success\n            if (true) {\n                // Clear any resume-related data from localStorage\n                try {\n                    const localStorageKeys = Object.keys(localStorage);\n                    const resumeKeys = localStorageKeys.filter((key)=>key.includes('resume') || key.includes('file') || key.includes('document'));\n                    if (resumeKeys.length > 0) {\n                        console.log('Clearing resume-related localStorage items:', resumeKeys);\n                        resumeKeys.forEach((key)=>localStorage.removeItem(key));\n                    }\n                    // Also try to clear specific keys that might be used for caching\n                    localStorage.removeItem('resume_cache');\n                    localStorage.removeItem('resume_list');\n                    localStorage.removeItem('profile_cache');\n                    localStorage.removeItem('resume_count');\n                    localStorage.removeItem('last_resume_update');\n                } catch (e) {\n                    console.error('Error clearing localStorage:', e);\n                }\n            }\n            return {\n                success,\n                message: success ? \"Resume deleted successfully\" : \"Resume deleted locally but server sync failed\"\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Resume deletion failed:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            // For UI purposes, return a success response even if backend fails\n            // This allows the UI to remove the resume entry and maintain a good user experience\n            return {\n                success: true,\n                synced: false,\n                error: error.message,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status,\n                message: \"Resume removed from display (sync with server failed)\"\n            };\n        }\n    },\n    // Upload certificate (10th or 12th)\n    uploadCertificate: async (file, type)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('certificate', file);\n        formData.append('type', type);\n        return api.post('/api/accounts/profiles/me/upload_certificate/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    },\n    // Get all certificates for the student\n    getCertificates: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        if (!token) {\n            throw new Error('Authentication required to fetch certificates');\n        }\n        try {\n            const response = await api.get('/api/accounts/profiles/me/certificates/', {\n                headers: {\n                    Authorization: \"Bearer \".concat(token)\n                }\n            });\n            // Ensure we're getting a proper response\n            if (!response.data) {\n                console.error('Empty response when fetching certificates');\n                return [];\n            }\n            // Handle different response formats\n            if (Array.isArray(response.data)) {\n                return response.data;\n            } else if (response.data.data && Array.isArray(response.data.data)) {\n                return response.data.data;\n            } else {\n                console.error('Unexpected certificate data format:', response.data);\n                return [];\n            }\n        } catch (error) {\n            var _error_response;\n            console.error('Certificate fetch error:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            throw error;\n        }\n    },\n    // Delete a specific certificate\n    deleteCertificate: async (certificateId)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        try {\n            console.log(\"Attempting to delete certificate with ID: \".concat(certificateId));\n            let success = false;\n            // Attempt different deletion strategies\n            const strategies = [\n                // Strategy 1: Standard DELETE request\n                async ()=>{\n                    try {\n                        const response = await api.delete(\"/api/accounts/profiles/me/certificates/\".concat(certificateId, \"/\"), {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('DELETE certificate successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 1 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 2: POST to remove endpoint\n                async ()=>{\n                    try {\n                        const response = await api.post(\"/api/accounts/profiles/me/certificates/\".concat(certificateId, \"/remove/\"), {}, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('POST remove successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 2 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 3: Patch profile with delete_certificate field\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            delete_certificate: certificateId\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('PATCH profile successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 3 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                },\n                // Strategy 4: Reset all certificates (extreme fallback)\n                async ()=>{\n                    try {\n                        const response = await api.patch('/api/auth/profile/', {\n                            reset_certificates: true\n                        }, {\n                            headers: {\n                                Authorization: \"Bearer \".concat(token)\n                            }\n                        });\n                        console.log('Reset certificates successful:', response.data);\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } catch (error) {\n                        console.log(\"Strategy 4 failed: \".concat(error.message));\n                        return {\n                            success: false\n                        };\n                    }\n                }\n            ];\n            // Try each strategy in sequence until one succeeds\n            for (const strategy of strategies){\n                const result = await strategy();\n                if (result.success) {\n                    success = true;\n                    break;\n                }\n            }\n            // Clear any locally cached data for this certificate regardless of backend success\n            if (true) {\n                // Clear any certificate-related data from localStorage\n                try {\n                    const localStorageKeys = Object.keys(localStorage);\n                    const certificateKeys = localStorageKeys.filter((key)=>key.includes('certificate') || key.includes('document') || key.includes('cert'));\n                    if (certificateKeys.length > 0) {\n                        console.log('Clearing certificate-related localStorage items:', certificateKeys);\n                        certificateKeys.forEach((key)=>localStorage.removeItem(key));\n                    }\n                    // Also try to clear specific keys that might be used for caching\n                    localStorage.removeItem('certificate_cache');\n                    localStorage.removeItem('certificate_list');\n                    localStorage.removeItem('profile_cache');\n                } catch (e) {\n                    console.error('Error clearing localStorage:', e);\n                }\n            }\n            return {\n                success,\n                message: success ? \"Certificate deleted successfully\" : \"Certificate deleted locally but server sync failed\"\n            };\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error('Certificate deletion failed:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status, error.message);\n            // For UI purposes, return a success response even if backend fails\n            // This allows the UI to remove the certificate entry and maintain a good user experience\n            return {\n                success: true,\n                synced: false,\n                error: error.message,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status,\n                message: \"Certificate removed from display (sync with server failed)\"\n            };\n        }\n    },\n    // Get semester marksheets\n    getSemesterMarksheets: async ()=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        return api.get('/api/accounts/profiles/me/semester_marksheets/', {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        }).then((response)=>response.data);\n    },\n    // Upload semester marksheet\n    uploadSemesterMarksheet: async (file, semester, cgpa)=>{\n        const token = (0,_auth__WEBPACK_IMPORTED_MODULE_0__.getAuthToken)();\n        const formData = new FormData();\n        formData.append('marksheet_file', file);\n        formData.append('semester', semester);\n        formData.append('cgpa', cgpa);\n        return api.post('/api/accounts/profiles/me/upload_semester_marksheet/', formData, {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                'Content-Type': 'multipart/form-data'\n            }\n        }).then((response)=>response.data);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcGkvc3R1ZGVudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBCO0FBQ1k7QUFFdEMsd0NBQXdDO0FBQ3hDLE1BQU1FLGVBQWU7QUFFckIsTUFBTUMsTUFBTUgsNkNBQUtBLENBQUNJLE1BQU0sQ0FBQztJQUN2QkMsU0FBU0g7SUFDVEksU0FBUztRQUNQLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsZ0RBQWdEO0FBQ2hESCxJQUFJSSxZQUFZLENBQUNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUMxQixDQUFDQztJQUNDLE1BQU1DLFFBQVFWLG1EQUFZQTtJQUMxQixJQUFJVSxPQUFPO1FBQ1RELE9BQU9KLE9BQU8sQ0FBQ00sYUFBYSxHQUFHLFVBQWdCLE9BQU5EO0lBQzNDO0lBQ0EsT0FBT0Q7QUFDVCxHQUNBLENBQUNHO0lBQ0MsT0FBT0MsUUFBUUMsTUFBTSxDQUFDRjtBQUN4QjtBQUdGLDhDQUE4QztBQUM5Q1YsSUFBSUksWUFBWSxDQUFDUyxRQUFRLENBQUNQLEdBQUcsQ0FDM0IsQ0FBQ08sV0FBYUEsVUFDZCxDQUFDSDtRQUNLQTtJQUFKLElBQUlBLEVBQUFBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sTUFBSyxLQUFLO1FBQ2xDLDJCQUEyQjtRQUMzQkMsYUFBYUMsVUFBVSxDQUFDO1FBQ3hCRCxhQUFhQyxVQUFVLENBQUM7UUFDeEJDLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO0lBQ3pCO0lBQ0EsT0FBT1IsUUFBUUMsTUFBTSxDQUFDRjtBQUN4QjtBQUdLLE1BQU1VLGNBQWM7SUFDekIsbUJBQW1CO0lBQ25CQyxhQUFhO1lBQU9DLDBFQUFTLENBQUM7UUFDNUIsTUFBTVQsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLDJCQUEyQjtZQUFFRDtRQUFPO1FBQ25FLE9BQU9ULFNBQVNXLElBQUk7SUFDdEI7SUFFQSxxQkFBcUI7SUFDckJDLFlBQVksT0FBT0M7UUFDakIsTUFBTWIsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLDBCQUE2QixPQUFIRyxJQUFHO1FBQzVELE9BQU9iLFNBQVNXLElBQUk7SUFDdEI7SUFFQSxpQkFBaUI7SUFDakJHLGVBQWUsT0FBT0QsSUFBSUY7UUFDeEJJLFFBQVFDLEdBQUcsQ0FBQyw4QkFBOEI7WUFBRUg7WUFBSUY7UUFBSztRQUVyRCx1QkFBdUI7UUFDdkIsTUFBTWhCLFFBQVFWLG1EQUFZQTtRQUMxQjhCLFFBQVFDLEdBQUcsQ0FBQyx5QkFBeUIsQ0FBQyxDQUFDckI7UUFDdkMsSUFBSUEsT0FBTztZQUNUb0IsUUFBUUMsR0FBRyxDQUFDLGtCQUFrQnJCLE1BQU1zQixTQUFTLENBQUMsR0FBRyxNQUFNO1FBQ3pEO1FBRUEsSUFBSSxDQUFDdEIsT0FBTztZQUNWLE1BQU0sSUFBSXVCLE1BQU07UUFDbEI7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTUMsY0FBYztZQUFFLEdBQUdSLElBQUk7UUFBQztRQUU5QiwrQ0FBK0M7UUFDL0M7WUFBQztZQUFnQjtTQUFlLENBQUNTLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDdkMsSUFBSUYsV0FBVyxDQUFDRSxNQUFNLEtBQUssUUFBUUYsV0FBVyxDQUFDRSxNQUFNLEtBQUtDLFdBQVc7Z0JBQ25FLE1BQU1DLE1BQU1DLFNBQVNMLFdBQVcsQ0FBQ0UsTUFBTTtnQkFDdkNGLFdBQVcsQ0FBQ0UsTUFBTSxHQUFHSSxNQUFNRixPQUFPLE9BQU9BO1lBQzNDO1FBQ0Y7UUFFQSw4Q0FBOEM7UUFDOUMsTUFBTUcsZUFBZTtZQUNuQjtZQUFjO1lBQWE7WUFBYztZQUFpQjtZQUFTO1lBQVU7WUFDN0U7WUFBaUI7WUFBVztZQUFRO1lBQVk7WUFBUztZQUFXO1lBQ3BFO1lBQWtCO1lBQWE7WUFDL0I7WUFBYztZQUFvQjtZQUFlO1lBQWdCO1lBQ2pFO1lBQWtCO1lBQ2xCO1lBQWdCO1lBQXNCO1lBQWlCO1lBQWtCO1lBQ3pFO1lBQW9CO1NBQ3JCO1FBRURBLGFBQWFOLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDbkIsSUFBSUYsV0FBVyxDQUFDRSxNQUFNLEtBQUssUUFBUUYsV0FBVyxDQUFDRSxNQUFNLEtBQUtDLFdBQVc7Z0JBQ25FSCxXQUFXLENBQUNFLE1BQU0sR0FBR00sT0FBT1IsV0FBVyxDQUFDRSxNQUFNLEVBQUVPLElBQUk7WUFDdEQ7UUFDRjtRQUVBLDBCQUEwQjtRQUMxQkMsT0FBT0MsSUFBSSxDQUFDWCxhQUFhQyxPQUFPLENBQUNXLENBQUFBO1lBQy9CLElBQUlaLFdBQVcsQ0FBQ1ksSUFBSSxLQUFLVCxXQUFXO2dCQUNsQyxPQUFPSCxXQUFXLENBQUNZLElBQUk7WUFDekI7UUFDRjtRQUVBaEIsUUFBUUMsR0FBRyxDQUFDLDRCQUE0Qkc7UUFFeEMsZ0RBQWdEO1FBQ2hELElBQUk7WUFDRkosUUFBUUMsR0FBRyxDQUFDLDRCQUE0QiwwQkFBNkIsT0FBSEgsSUFBRztZQUNyRSxNQUFNYixXQUFXLE1BQU1iLElBQUk2QyxLQUFLLENBQUMsMEJBQTZCLE9BQUhuQixJQUFHLE1BQUlNO1lBQ2xFSixRQUFRQyxHQUFHLENBQUMsNkJBQTZCaEIsU0FBU1csSUFBSTtZQUN0RCxPQUFPWCxTQUFTVyxJQUFJO1FBQ3RCLEVBQUUsT0FBT2QsT0FBTztnQkFFSkEsaUJBQ0lBLGtCQUNOQSxrQkFDR0E7WUFKWGtCLFFBQVFsQixLQUFLLENBQUMsNEJBQTRCO2dCQUN4Q0ksTUFBTSxHQUFFSixrQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCxzQ0FBQUEsZ0JBQWdCSSxNQUFNO2dCQUM5QmdDLFVBQVUsR0FBRXBDLG1CQUFBQSxNQUFNRyxRQUFRLGNBQWRILHVDQUFBQSxpQkFBZ0JvQyxVQUFVO2dCQUN0Q3RCLElBQUksR0FBRWQsbUJBQUFBLE1BQU1HLFFBQVEsY0FBZEgsdUNBQUFBLGlCQUFnQmMsSUFBSTtnQkFDMUJyQixPQUFPLEdBQUVPLG1CQUFBQSxNQUFNRyxRQUFRLGNBQWRILHVDQUFBQSxpQkFBZ0JQLE9BQU87Z0JBQ2hDSSxRQUFRRyxNQUFNSCxNQUFNO1lBQ3RCO1lBRUEsOENBQThDO1lBQzlDLElBQUk7Z0JBQ0ZxQixRQUFRQyxHQUFHLENBQUMsNkJBQTZCLDBCQUE2QixPQUFISCxJQUFHO2dCQUN0RSxNQUFNYixXQUFXLE1BQU1iLElBQUk2QyxLQUFLLENBQUMsMEJBQTZCLE9BQUhuQixJQUFHLGFBQVdNO2dCQUN6RUosUUFBUUMsR0FBRyxDQUFDLDhCQUE4QmhCLFNBQVNXLElBQUk7Z0JBQ3ZELE9BQU9YLFNBQVNXLElBQUk7WUFDdEIsRUFBRSxPQUFPdUIsYUFBYTtvQkFHUnJDLGtCQUNGQSxrQkFHRXFDLHVCQUNGQSx3QkFLV0E7Z0JBWnJCbkIsUUFBUWxCLEtBQUssQ0FBQyxnREFBZ0Q7b0JBQzVEc0MsY0FBYzt3QkFDWmxDLE1BQU0sR0FBRUosbUJBQUFBLE1BQU1HLFFBQVEsY0FBZEgsdUNBQUFBLGlCQUFnQkksTUFBTTt3QkFDOUJVLElBQUksR0FBRWQsbUJBQUFBLE1BQU1HLFFBQVEsY0FBZEgsdUNBQUFBLGlCQUFnQmMsSUFBSTtvQkFDNUI7b0JBQ0F5QixpQkFBaUI7d0JBQ2ZuQyxNQUFNLEdBQUVpQyx3QkFBQUEsWUFBWWxDLFFBQVEsY0FBcEJrQyw0Q0FBQUEsc0JBQXNCakMsTUFBTTt3QkFDcENVLElBQUksR0FBRXVCLHlCQUFBQSxZQUFZbEMsUUFBUSxjQUFwQmtDLDZDQUFBQSx1QkFBc0J2QixJQUFJO29CQUNsQztnQkFDRjtnQkFFQSxnQ0FBZ0M7Z0JBQ2hDLE1BQU0wQixlQUFlSCxFQUFBQSx5QkFBQUEsWUFBWWxDLFFBQVEsY0FBcEJrQyw2Q0FBQUEsdUJBQXNCakMsTUFBTSxNQUFLLE1BQU1pQyxjQUFjckM7Z0JBQzFFLE1BQU13QztZQUNSO1FBQ0Y7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQkMsWUFBWTtRQUNWLE1BQU0zQyxRQUFRVixtREFBWUE7UUFDMUIsT0FBT0UsSUFBSXVCLEdBQUcsQ0FBQyxzQkFBc0I7WUFDbkNwQixTQUFTO2dCQUFFTSxlQUFlLFVBQWdCLE9BQU5EO1lBQVE7UUFDOUMsR0FBRzRDLElBQUksQ0FBQyxDQUFDdkMsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztJQUVBLDZCQUE2QjtJQUM3QjZCLGVBQWUsT0FBTzdCO1FBQ3BCLE1BQU1oQixRQUFRVixtREFBWUE7UUFDMUIsT0FBT0UsSUFBSTZDLEtBQUssQ0FBQyxzQkFBc0JyQixNQUFNO1lBQzNDckIsU0FBUztnQkFBRU0sZUFBZSxVQUFnQixPQUFORDtZQUFRO1FBQzlDLEdBQUc0QyxJQUFJLENBQUMsQ0FBQ3ZDLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSx1QkFBdUI7SUFDdkI4QixvQkFBb0IsT0FBT0M7UUFDekIsTUFBTS9DLFFBQVFWLG1EQUFZQTtRQUMxQixNQUFNMEQsV0FBVyxJQUFJQztRQUNyQkQsU0FBU0UsTUFBTSxDQUFDLFNBQVNIO1FBRXpCLE9BQU92RCxJQUFJMkQsSUFBSSxDQUFDLG1EQUFtREgsVUFBVTtZQUMzRXJELFNBQVM7Z0JBQ1BNLGVBQWUsVUFBZ0IsT0FBTkQ7Z0JBQ3pCLGdCQUFnQjtZQUNsQjtRQUNGLEdBQUc0QyxJQUFJLENBQUMsQ0FBQ3ZDLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSxnQkFBZ0I7SUFDaEJvQyxjQUFjLE9BQU9MO1FBQ25CLE1BQU0vQyxRQUFRVixtREFBWUE7UUFDMUIsTUFBTTBELFdBQVcsSUFBSUM7UUFDckJELFNBQVNFLE1BQU0sQ0FBQyxVQUFVSDtRQUUxQixPQUFPdkQsSUFBSTZDLEtBQUssQ0FBQyxzQkFBc0JXLFVBQVU7WUFDL0NyRCxTQUFTO2dCQUNQTSxlQUFlLFVBQWdCLE9BQU5EO2dCQUN6QixnQkFBZ0I7WUFDbEI7UUFDRixHQUFHNEMsSUFBSSxDQUFDLENBQUN2QyxXQUFhQSxTQUFTVyxJQUFJO0lBQ3JDO0lBRUEsa0NBQWtDO0lBQ2xDcUMsWUFBWTtRQUNWLE1BQU1yRCxRQUFRVixtREFBWUE7UUFDMUIsSUFBSSxDQUFDVSxPQUFPO1lBQ1YsTUFBTSxJQUFJdUIsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixNQUFNbEIsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLHNDQUFzQztnQkFDbkVwQixTQUFTO29CQUFFTSxlQUFlLFVBQWdCLE9BQU5EO2dCQUFRO1lBQzlDO1lBRUEseUNBQXlDO1lBQ3pDLElBQUksQ0FBQ0ssU0FBU1csSUFBSSxFQUFFO2dCQUNsQkksUUFBUWxCLEtBQUssQ0FBQztnQkFDZCxPQUFPLEVBQUU7WUFDWDtZQUVBLG9DQUFvQztZQUNwQyxJQUFJb0QsTUFBTUMsT0FBTyxDQUFDbEQsU0FBU1csSUFBSSxHQUFHO2dCQUNoQyxPQUFPWCxTQUFTVyxJQUFJO1lBQ3RCLE9BQU8sSUFBSVgsU0FBU1csSUFBSSxDQUFDQSxJQUFJLElBQUlzQyxNQUFNQyxPQUFPLENBQUNsRCxTQUFTVyxJQUFJLENBQUNBLElBQUksR0FBRztnQkFDbEUsT0FBT1gsU0FBU1csSUFBSSxDQUFDQSxJQUFJO1lBQzNCLE9BQU87Z0JBQ0xJLFFBQVFsQixLQUFLLENBQUMsa0NBQWtDRyxTQUFTVyxJQUFJO2dCQUM3RCxPQUFPLEVBQUU7WUFDWDtRQUNGLEVBQUUsT0FBT2QsT0FBTztnQkFDdUJBO1lBQXJDa0IsUUFBUWxCLEtBQUssQ0FBQyx3QkFBdUJBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sRUFBRUosTUFBTXNELE9BQU87WUFDMUUsTUFBTXREO1FBQ1I7SUFDRjtJQUVBLDJCQUEyQjtJQUMzQnVELGNBQWMsT0FBT0M7UUFDbkIsTUFBTTFELFFBQVFWLG1EQUFZQTtRQUMxQixJQUFJO1lBQ0Y4QixRQUFRQyxHQUFHLENBQUMsd0NBQWlELE9BQVRxQztZQUVwRCxJQUFJQyxVQUFVO1lBRWQsd0NBQXdDO1lBQ3hDLE1BQU1DLGFBQWE7Z0JBQ2pCLHNDQUFzQztnQkFDdEM7b0JBQ0UsSUFBSTt3QkFDRixNQUFNdkQsV0FBVyxNQUFNYixJQUFJcUUsTUFBTSxDQUFDLHFDQUE4QyxPQUFUSCxVQUFTLE1BQUk7NEJBQ2xGL0QsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FvQixRQUFRQyxHQUFHLENBQUMsNkJBQTZCaEIsU0FBU1csSUFBSTt3QkFDdEQsT0FBTzs0QkFBRTJDLFNBQVM7NEJBQU0zQyxNQUFNWCxTQUFTVyxJQUFJO3dCQUFDO29CQUM5QyxFQUFFLE9BQU9kLE9BQU87d0JBQ2RrQixRQUFRQyxHQUFHLENBQUMsc0JBQW9DLE9BQWRuQixNQUFNc0QsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUcsU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7Z0JBRUEsc0NBQXNDO2dCQUN0QztvQkFDRSxJQUFJO3dCQUNGLE1BQU10RCxXQUFXLE1BQU1iLElBQUkyRCxJQUFJLENBQUMscUNBQThDLE9BQVRPLFVBQVMsYUFBVyxDQUFDLEdBQUc7NEJBQzNGL0QsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FvQixRQUFRQyxHQUFHLENBQUMsMkJBQTJCaEIsU0FBU1csSUFBSTt3QkFDcEQsT0FBTzs0QkFBRTJDLFNBQVM7NEJBQU0zQyxNQUFNWCxTQUFTVyxJQUFJO3dCQUFDO29CQUM5QyxFQUFFLE9BQU9kLE9BQU87d0JBQ2RrQixRQUFRQyxHQUFHLENBQUMsc0JBQW9DLE9BQWRuQixNQUFNc0QsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUcsU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7Z0JBRUEscURBQXFEO2dCQUNyRDtvQkFDRSxJQUFJO3dCQUNGLE1BQU10RCxXQUFXLE1BQU1iLElBQUk2QyxLQUFLLENBQUMsc0JBQXNCOzRCQUNyRHlCLGVBQWVKO3dCQUNqQixHQUFHOzRCQUNEL0QsU0FBUztnQ0FBRU0sZUFBZSxVQUFnQixPQUFORDs0QkFBUTt3QkFDOUM7d0JBQ0FvQixRQUFRQyxHQUFHLENBQUMsNkJBQTZCaEIsU0FBU1csSUFBSTt3QkFDdEQsT0FBTzs0QkFBRTJDLFNBQVM7NEJBQU0zQyxNQUFNWCxTQUFTVyxJQUFJO3dCQUFDO29CQUM5QyxFQUFFLE9BQU9kLE9BQU87d0JBQ2RrQixRQUFRQyxHQUFHLENBQUMsc0JBQW9DLE9BQWRuQixNQUFNc0QsT0FBTzt3QkFDL0MsT0FBTzs0QkFBRUcsU0FBUzt3QkFBTTtvQkFDMUI7Z0JBQ0Y7Z0JBRUEsbURBQW1EO2dCQUNuRDtvQkFDRSxJQUFJO3dCQUNGLE1BQU10RCxXQUFXLE1BQU1iLElBQUk2QyxLQUFLLENBQUMsc0JBQXNCOzRCQUNyRDBCLGVBQWU7d0JBQ2pCLEdBQUc7NEJBQ0RwRSxTQUFTO2dDQUFFTSxlQUFlLFVBQWdCLE9BQU5EOzRCQUFRO3dCQUM5Qzt3QkFDQW9CLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJoQixTQUFTVyxJQUFJO3dCQUN0RCxPQUFPOzRCQUFFMkMsU0FBUzs0QkFBTTNDLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZGtCLFFBQVFDLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZG5CLE1BQU1zRCxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFRyxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjthQUNEO1lBRUQsbURBQW1EO1lBQ25ELEtBQUssTUFBTUssWUFBWUosV0FBWTtnQkFDakMsTUFBTUssU0FBUyxNQUFNRDtnQkFDckIsSUFBSUMsT0FBT04sT0FBTyxFQUFFO29CQUNsQkEsVUFBVTtvQkFDVjtnQkFDRjtZQUNGO1lBRUEsOEVBQThFO1lBQzlFLElBQUksSUFBNkIsRUFBRTtnQkFDakMsa0RBQWtEO2dCQUNsRCxJQUFJO29CQUNGLE1BQU1PLG1CQUFtQmhDLE9BQU9DLElBQUksQ0FBQzVCO29CQUNyQyxNQUFNNEQsYUFBYUQsaUJBQWlCRSxNQUFNLENBQUNoQyxDQUFBQSxNQUN6Q0EsSUFBSWlDLFFBQVEsQ0FBQyxhQUFhakMsSUFBSWlDLFFBQVEsQ0FBQyxXQUFXakMsSUFBSWlDLFFBQVEsQ0FBQztvQkFHakUsSUFBSUYsV0FBV0csTUFBTSxHQUFHLEdBQUc7d0JBQ3pCbEQsUUFBUUMsR0FBRyxDQUFDLCtDQUErQzhDO3dCQUMzREEsV0FBVzFDLE9BQU8sQ0FBQ1csQ0FBQUEsTUFBTzdCLGFBQWFDLFVBQVUsQ0FBQzRCO29CQUNwRDtvQkFFQSxpRUFBaUU7b0JBQ2pFN0IsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO29CQUN4QkQsYUFBYUMsVUFBVSxDQUFDO2dCQUMxQixFQUFFLE9BQU8rRCxHQUFHO29CQUNWbkQsUUFBUWxCLEtBQUssQ0FBQyxnQ0FBZ0NxRTtnQkFDaEQ7WUFDRjtZQUVBLE9BQU87Z0JBQUVaO2dCQUFTSCxTQUFTRyxVQUFVLGdDQUFnQztZQUFnRDtRQUN2SCxFQUFFLE9BQU96RCxPQUFPO2dCQUMyQkEsaUJBTy9CQTtZQVBWa0IsUUFBUWxCLEtBQUssQ0FBQyw0QkFBMkJBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sRUFBRUosTUFBTXNELE9BQU87WUFDOUUsbUVBQW1FO1lBQ25FLG9GQUFvRjtZQUNwRixPQUFPO2dCQUNMRyxTQUFTO2dCQUNUYSxRQUFRO2dCQUNSdEUsT0FBT0EsTUFBTXNELE9BQU87Z0JBQ3BCbEQsTUFBTSxHQUFFSixtQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCx1Q0FBQUEsaUJBQWdCSSxNQUFNO2dCQUM5QmtELFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxvQ0FBb0M7SUFDcENpQixtQkFBbUIsT0FBTzFCLE1BQU0yQjtRQUM5QixNQUFNMUUsUUFBUVYsbURBQVlBO1FBQzFCLE1BQU0wRCxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsZUFBZUg7UUFDL0JDLFNBQVNFLE1BQU0sQ0FBQyxRQUFRd0I7UUFFeEIsT0FBT2xGLElBQUkyRCxJQUFJLENBQUMsaURBQWlESCxVQUFVO1lBQ3pFckQsU0FBUztnQkFDUE0sZUFBZSxVQUFnQixPQUFORDtnQkFDekIsZ0JBQWdCO1lBQ2xCO1FBQ0YsR0FBRzRDLElBQUksQ0FBQyxDQUFDdkMsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztJQUVBLHVDQUF1QztJQUN2QzJELGlCQUFpQjtRQUNmLE1BQU0zRSxRQUFRVixtREFBWUE7UUFDMUIsSUFBSSxDQUFDVSxPQUFPO1lBQ1YsTUFBTSxJQUFJdUIsTUFBTTtRQUNsQjtRQUVBLElBQUk7WUFDRixNQUFNbEIsV0FBVyxNQUFNYixJQUFJdUIsR0FBRyxDQUFDLDJDQUEyQztnQkFDeEVwQixTQUFTO29CQUFFTSxlQUFlLFVBQWdCLE9BQU5EO2dCQUFRO1lBQzlDO1lBRUEseUNBQXlDO1lBQ3pDLElBQUksQ0FBQ0ssU0FBU1csSUFBSSxFQUFFO2dCQUNsQkksUUFBUWxCLEtBQUssQ0FBQztnQkFDZCxPQUFPLEVBQUU7WUFDWDtZQUVBLG9DQUFvQztZQUNwQyxJQUFJb0QsTUFBTUMsT0FBTyxDQUFDbEQsU0FBU1csSUFBSSxHQUFHO2dCQUNoQyxPQUFPWCxTQUFTVyxJQUFJO1lBQ3RCLE9BQU8sSUFBSVgsU0FBU1csSUFBSSxDQUFDQSxJQUFJLElBQUlzQyxNQUFNQyxPQUFPLENBQUNsRCxTQUFTVyxJQUFJLENBQUNBLElBQUksR0FBRztnQkFDbEUsT0FBT1gsU0FBU1csSUFBSSxDQUFDQSxJQUFJO1lBQzNCLE9BQU87Z0JBQ0xJLFFBQVFsQixLQUFLLENBQUMsdUNBQXVDRyxTQUFTVyxJQUFJO2dCQUNsRSxPQUFPLEVBQUU7WUFDWDtRQUNGLEVBQUUsT0FBT2QsT0FBTztnQkFDNEJBO1lBQTFDa0IsUUFBUWxCLEtBQUssQ0FBQyw2QkFBNEJBLGtCQUFBQSxNQUFNRyxRQUFRLGNBQWRILHNDQUFBQSxnQkFBZ0JJLE1BQU0sRUFBRUosTUFBTXNELE9BQU87WUFDL0UsTUFBTXREO1FBQ1I7SUFDRjtJQUVBLGdDQUFnQztJQUNoQzBFLG1CQUFtQixPQUFPQztRQUN4QixNQUFNN0UsUUFBUVYsbURBQVlBO1FBQzFCLElBQUk7WUFDRjhCLFFBQVFDLEdBQUcsQ0FBQyw2Q0FBMkQsT0FBZHdEO1lBRXpELElBQUlsQixVQUFVO1lBRWQsd0NBQXdDO1lBQ3hDLE1BQU1DLGFBQWE7Z0JBQ2pCLHNDQUFzQztnQkFDdEM7b0JBQ0UsSUFBSTt3QkFDRixNQUFNdkQsV0FBVyxNQUFNYixJQUFJcUUsTUFBTSxDQUFDLDBDQUF3RCxPQUFkZ0IsZUFBYyxNQUFJOzRCQUM1RmxGLFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBb0IsUUFBUUMsR0FBRyxDQUFDLGtDQUFrQ2hCLFNBQVNXLElBQUk7d0JBQzNELE9BQU87NEJBQUUyQyxTQUFTOzRCQUFNM0MsTUFBTVgsU0FBU1csSUFBSTt3QkFBQztvQkFDOUMsRUFBRSxPQUFPZCxPQUFPO3dCQUNka0IsUUFBUUMsR0FBRyxDQUFDLHNCQUFvQyxPQUFkbkIsTUFBTXNELE9BQU87d0JBQy9DLE9BQU87NEJBQUVHLFNBQVM7d0JBQU07b0JBQzFCO2dCQUNGO2dCQUVBLHNDQUFzQztnQkFDdEM7b0JBQ0UsSUFBSTt3QkFDRixNQUFNdEQsV0FBVyxNQUFNYixJQUFJMkQsSUFBSSxDQUFDLDBDQUF3RCxPQUFkMEIsZUFBYyxhQUFXLENBQUMsR0FBRzs0QkFDckdsRixTQUFTO2dDQUFFTSxlQUFlLFVBQWdCLE9BQU5EOzRCQUFRO3dCQUM5Qzt3QkFDQW9CLFFBQVFDLEdBQUcsQ0FBQywyQkFBMkJoQixTQUFTVyxJQUFJO3dCQUNwRCxPQUFPOzRCQUFFMkMsU0FBUzs0QkFBTTNDLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZGtCLFFBQVFDLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZG5CLE1BQU1zRCxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFRyxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjtnQkFFQSwwREFBMEQ7Z0JBQzFEO29CQUNFLElBQUk7d0JBQ0YsTUFBTXRELFdBQVcsTUFBTWIsSUFBSTZDLEtBQUssQ0FBQyxzQkFBc0I7NEJBQ3JEeUMsb0JBQW9CRDt3QkFDdEIsR0FBRzs0QkFDRGxGLFNBQVM7Z0NBQUVNLGVBQWUsVUFBZ0IsT0FBTkQ7NEJBQVE7d0JBQzlDO3dCQUNBb0IsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QmhCLFNBQVNXLElBQUk7d0JBQ3RELE9BQU87NEJBQUUyQyxTQUFTOzRCQUFNM0MsTUFBTVgsU0FBU1csSUFBSTt3QkFBQztvQkFDOUMsRUFBRSxPQUFPZCxPQUFPO3dCQUNka0IsUUFBUUMsR0FBRyxDQUFDLHNCQUFvQyxPQUFkbkIsTUFBTXNELE9BQU87d0JBQy9DLE9BQU87NEJBQUVHLFNBQVM7d0JBQU07b0JBQzFCO2dCQUNGO2dCQUVBLHdEQUF3RDtnQkFDeEQ7b0JBQ0UsSUFBSTt3QkFDRixNQUFNdEQsV0FBVyxNQUFNYixJQUFJNkMsS0FBSyxDQUFDLHNCQUFzQjs0QkFDckQwQyxvQkFBb0I7d0JBQ3RCLEdBQUc7NEJBQ0RwRixTQUFTO2dDQUFFTSxlQUFlLFVBQWdCLE9BQU5EOzRCQUFRO3dCQUM5Qzt3QkFDQW9CLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NoQixTQUFTVyxJQUFJO3dCQUMzRCxPQUFPOzRCQUFFMkMsU0FBUzs0QkFBTTNDLE1BQU1YLFNBQVNXLElBQUk7d0JBQUM7b0JBQzlDLEVBQUUsT0FBT2QsT0FBTzt3QkFDZGtCLFFBQVFDLEdBQUcsQ0FBQyxzQkFBb0MsT0FBZG5CLE1BQU1zRCxPQUFPO3dCQUMvQyxPQUFPOzRCQUFFRyxTQUFTO3dCQUFNO29CQUMxQjtnQkFDRjthQUNEO1lBRUQsbURBQW1EO1lBQ25ELEtBQUssTUFBTUssWUFBWUosV0FBWTtnQkFDakMsTUFBTUssU0FBUyxNQUFNRDtnQkFDckIsSUFBSUMsT0FBT04sT0FBTyxFQUFFO29CQUNsQkEsVUFBVTtvQkFDVjtnQkFDRjtZQUNGO1lBRUEsbUZBQW1GO1lBQ25GLElBQUksSUFBNkIsRUFBRTtnQkFDakMsdURBQXVEO2dCQUN2RCxJQUFJO29CQUNGLE1BQU1PLG1CQUFtQmhDLE9BQU9DLElBQUksQ0FBQzVCO29CQUNyQyxNQUFNeUUsa0JBQWtCZCxpQkFBaUJFLE1BQU0sQ0FBQ2hDLENBQUFBLE1BQzlDQSxJQUFJaUMsUUFBUSxDQUFDLGtCQUFrQmpDLElBQUlpQyxRQUFRLENBQUMsZUFBZWpDLElBQUlpQyxRQUFRLENBQUM7b0JBRzFFLElBQUlXLGdCQUFnQlYsTUFBTSxHQUFHLEdBQUc7d0JBQzlCbEQsUUFBUUMsR0FBRyxDQUFDLG9EQUFvRDJEO3dCQUNoRUEsZ0JBQWdCdkQsT0FBTyxDQUFDVyxDQUFBQSxNQUFPN0IsYUFBYUMsVUFBVSxDQUFDNEI7b0JBQ3pEO29CQUVBLGlFQUFpRTtvQkFDakU3QixhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7b0JBQ3hCRCxhQUFhQyxVQUFVLENBQUM7Z0JBQzFCLEVBQUUsT0FBTytELEdBQUc7b0JBQ1ZuRCxRQUFRbEIsS0FBSyxDQUFDLGdDQUFnQ3FFO2dCQUNoRDtZQUNGO1lBRUEsT0FBTztnQkFBRVo7Z0JBQVNILFNBQVNHLFVBQVUscUNBQXFDO1lBQXFEO1FBQ2pJLEVBQUUsT0FBT3pELE9BQU87Z0JBQ2dDQSxpQkFPcENBO1lBUFZrQixRQUFRbEIsS0FBSyxDQUFDLGlDQUFnQ0Esa0JBQUFBLE1BQU1HLFFBQVEsY0FBZEgsc0NBQUFBLGdCQUFnQkksTUFBTSxFQUFFSixNQUFNc0QsT0FBTztZQUNuRixtRUFBbUU7WUFDbkUseUZBQXlGO1lBQ3pGLE9BQU87Z0JBQ0xHLFNBQVM7Z0JBQ1RhLFFBQVE7Z0JBQ1J0RSxPQUFPQSxNQUFNc0QsT0FBTztnQkFDcEJsRCxNQUFNLEdBQUVKLG1CQUFBQSxNQUFNRyxRQUFRLGNBQWRILHVDQUFBQSxpQkFBZ0JJLE1BQU07Z0JBQzlCa0QsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQnlCLHVCQUF1QjtRQUNyQixNQUFNakYsUUFBUVYsbURBQVlBO1FBQzFCLE9BQU9FLElBQUl1QixHQUFHLENBQUMsa0RBQWtEO1lBQy9EcEIsU0FBUztnQkFBRU0sZUFBZSxVQUFnQixPQUFORDtZQUFRO1FBQzlDLEdBQUc0QyxJQUFJLENBQUMsQ0FBQ3ZDLFdBQWFBLFNBQVNXLElBQUk7SUFDckM7SUFFQSw0QkFBNEI7SUFDNUJrRSx5QkFBeUIsT0FBT25DLE1BQU1vQyxVQUFVQztRQUM5QyxNQUFNcEYsUUFBUVYsbURBQVlBO1FBQzFCLE1BQU0wRCxXQUFXLElBQUlDO1FBQ3JCRCxTQUFTRSxNQUFNLENBQUMsa0JBQWtCSDtRQUNsQ0MsU0FBU0UsTUFBTSxDQUFDLFlBQVlpQztRQUM1Qm5DLFNBQVNFLE1BQU0sQ0FBQyxRQUFRa0M7UUFFeEIsT0FBTzVGLElBQUkyRCxJQUFJLENBQUMsd0RBQXdESCxVQUFVO1lBQ2hGckQsU0FBUztnQkFDUE0sZUFBZSxVQUFnQixPQUFORDtnQkFDekIsZ0JBQWdCO1lBQ2xCO1FBQ0YsR0FBRzRDLElBQUksQ0FBQyxDQUFDdkMsV0FBYUEsU0FBU1csSUFBSTtJQUNyQztBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccGdhdXRcXERvY3VtZW50c1xcVlMgQ09ERVxcY29tYmluZVxcZnJvbnRlbmRcXHNyY1xcYXBpXFxzdHVkZW50cy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYXhpb3MgZnJvbSAnYXhpb3MnO1xyXG5pbXBvcnQgeyBnZXRBdXRoVG9rZW4gfSBmcm9tICcuL2F1dGgnO1xyXG5cclxuLy8gU2V0IHRoZSBiYXNlIFVSTCBmb3IgYWxsIEFQSSByZXF1ZXN0c1xyXG5jb25zdCBBUElfQkFTRV9VUkwgPSAnaHR0cDovL2xvY2FsaG9zdDo4MDAwJztcclxuXHJcbmNvbnN0IGFwaSA9IGF4aW9zLmNyZWF0ZSh7XHJcbiAgYmFzZVVSTDogQVBJX0JBU0VfVVJMLFxyXG4gIGhlYWRlcnM6IHtcclxuICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXHJcbiAgfSxcclxufSk7XHJcblxyXG4vLyBBZGQgcmVxdWVzdCBpbnRlcmNlcHRvciB0byBpbmNsdWRlIGF1dGggdG9rZW5cclxuYXBpLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShcclxuICAoY29uZmlnKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgaWYgKHRva2VuKSB7XHJcbiAgICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDtcclxuICAgIH1cclxuICAgIHJldHVybiBjb25maWc7XHJcbiAgfSxcclxuICAoZXJyb3IpID0+IHtcclxuICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7XHJcbiAgfVxyXG4pO1xyXG5cclxuLy8gQWRkIHJlc3BvbnNlIGludGVyY2VwdG9yIGZvciBlcnJvciBoYW5kbGluZ1xyXG5hcGkuaW50ZXJjZXB0b3JzLnJlc3BvbnNlLnVzZShcclxuICAocmVzcG9uc2UpID0+IHJlc3BvbnNlLFxyXG4gIChlcnJvcikgPT4ge1xyXG4gICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xyXG4gICAgICAvLyBUb2tlbiBleHBpcmVkIG9yIGludmFsaWRcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FjY2Vzc190b2tlbicpO1xyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncmVmcmVzaF90b2tlbicpO1xyXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGVycm9yKTtcclxuICB9XHJcbik7XHJcblxyXG5leHBvcnQgY29uc3Qgc3R1ZGVudHNBUEkgPSB7XHJcbiAgLy8gR2V0IGFsbCBzdHVkZW50c1xyXG4gIGdldFN0dWRlbnRzOiBhc3luYyAocGFyYW1zID0ge30pID0+IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2FwaS9hY2NvdW50cy9zdHVkZW50cy8nLCB7IHBhcmFtcyB9KTtcclxuICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBzaW5nbGUgc3R1ZGVudFxyXG4gIGdldFN0dWRlbnQ6IGFzeW5jIChpZCkgPT4ge1xyXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KGAvYXBpL2FjY291bnRzL3N0dWRlbnRzLyR7aWR9L2ApO1xyXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGE7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBkYXRlIHN0dWRlbnRcclxuICB1cGRhdGVTdHVkZW50OiBhc3luYyAoaWQsIGRhdGEpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCd1cGRhdGVTdHVkZW50IGNhbGxlZCB3aXRoOicsIHsgaWQsIGRhdGEgfSk7XHJcblxyXG4gICAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb25cclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICBjb25zb2xlLmxvZygnQXV0aCB0b2tlbiBhdmFpbGFibGU6JywgISF0b2tlbik7XHJcbiAgICBpZiAodG9rZW4pIHtcclxuICAgICAgY29uc29sZS5sb2coJ1Rva2VuIHByZXZpZXc6JywgdG9rZW4uc3Vic3RyaW5nKDAsIDIwKSArICcuLi4nKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAoIXRva2VuKSB7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcignQXV0aGVudGljYXRpb24gcmVxdWlyZWQgdG8gdXBkYXRlIHN0dWRlbnQnKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhbiBkYXRhIHRvIGVuc3VyZSBwcm9wZXIgZm9ybWF0XHJcbiAgICBjb25zdCBjbGVhbmVkRGF0YSA9IHsgLi4uZGF0YSB9O1xyXG4gICAgXHJcbiAgICAvLyBFbnN1cmUgbnVtZXJpYyBmaWVsZHMgYXJlIHByb3Blcmx5IGZvcm1hdHRlZFxyXG4gICAgWydqb2luaW5nX3llYXInLCAncGFzc291dF95ZWFyJ10uZm9yRWFjaChmaWVsZCA9PiB7XHJcbiAgICAgIGlmIChjbGVhbmVkRGF0YVtmaWVsZF0gIT09IG51bGwgJiYgY2xlYW5lZERhdGFbZmllbGRdICE9PSB1bmRlZmluZWQpIHtcclxuICAgICAgICBjb25zdCBudW0gPSBwYXJzZUludChjbGVhbmVkRGF0YVtmaWVsZF0pO1xyXG4gICAgICAgIGNsZWFuZWREYXRhW2ZpZWxkXSA9IGlzTmFOKG51bSkgPyBudWxsIDogbnVtO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBFbnN1cmUgc3RyaW5nIGZpZWxkcyBhcmUgcHJvcGVybHkgZm9ybWF0dGVkXHJcbiAgICBjb25zdCBzdHJpbmdGaWVsZHMgPSBbXHJcbiAgICAgICdmaXJzdF9uYW1lJywgJ2xhc3RfbmFtZScsICdzdHVkZW50X2lkJywgJ2NvbnRhY3RfZW1haWwnLCAncGhvbmUnLCAnYnJhbmNoJywgJ2dwYScsXHJcbiAgICAgICdkYXRlX29mX2JpcnRoJywgJ2FkZHJlc3MnLCAnY2l0eScsICdkaXN0cmljdCcsICdzdGF0ZScsICdwaW5jb2RlJywgJ2NvdW50cnknLFxyXG4gICAgICAncGFyZW50X2NvbnRhY3QnLCAnZWR1Y2F0aW9uJywgJ3NraWxscycsXHJcbiAgICAgICd0ZW50aF9jZ3BhJywgJ3RlbnRoX3BlcmNlbnRhZ2UnLCAndGVudGhfYm9hcmQnLCAndGVudGhfc2Nob29sJywgJ3RlbnRoX3llYXJfb2ZfcGFzc2luZycsIFxyXG4gICAgICAndGVudGhfbG9jYXRpb24nLCAndGVudGhfc3BlY2lhbGl6YXRpb24nLFxyXG4gICAgICAndHdlbGZ0aF9jZ3BhJywgJ3R3ZWxmdGhfcGVyY2VudGFnZScsICd0d2VsZnRoX2JvYXJkJywgJ3R3ZWxmdGhfc2Nob29sJywgJ3R3ZWxmdGhfeWVhcl9vZl9wYXNzaW5nJyxcclxuICAgICAgJ3R3ZWxmdGhfbG9jYXRpb24nLCAndHdlbGZ0aF9zcGVjaWFsaXphdGlvbidcclxuICAgIF07XHJcblxyXG4gICAgc3RyaW5nRmllbGRzLmZvckVhY2goZmllbGQgPT4ge1xyXG4gICAgICBpZiAoY2xlYW5lZERhdGFbZmllbGRdICE9PSBudWxsICYmIGNsZWFuZWREYXRhW2ZpZWxkXSAhPT0gdW5kZWZpbmVkKSB7XHJcbiAgICAgICAgY2xlYW5lZERhdGFbZmllbGRdID0gU3RyaW5nKGNsZWFuZWREYXRhW2ZpZWxkXSkudHJpbSgpO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICAvLyBSZW1vdmUgdW5kZWZpbmVkIHZhbHVlc1xyXG4gICAgT2JqZWN0LmtleXMoY2xlYW5lZERhdGEpLmZvckVhY2goa2V5ID0+IHtcclxuICAgICAgaWYgKGNsZWFuZWREYXRhW2tleV0gPT09IHVuZGVmaW5lZCkge1xyXG4gICAgICAgIGRlbGV0ZSBjbGVhbmVkRGF0YVtrZXldO1xyXG4gICAgICB9XHJcbiAgICB9KTtcclxuXHJcbiAgICBjb25zb2xlLmxvZygnQ2xlYW5lZCBkYXRhIGJlaW5nIHNlbnQ6JywgY2xlYW5lZERhdGEpO1xyXG5cclxuICAgIC8vIFRyeSB0aGUgVmlld1NldCBlbmRwb2ludCBmaXJzdCAobW9yZSBSRVNUZnVsKVxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coJ1RyeWluZyBWaWV3U2V0IGVuZHBvaW50OicsIGAvYXBpL2FjY291bnRzL3Byb2ZpbGVzLyR7aWR9L2ApO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wYXRjaChgL2FwaS9hY2NvdW50cy9wcm9maWxlcy8ke2lkfS9gLCBjbGVhbmVkRGF0YSk7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdWaWV3U2V0IGVuZHBvaW50IHN1Y2Nlc3M6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignVmlld1NldCBlbmRwb2ludCBmYWlsZWQ6Jywge1xyXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICBzdGF0dXNUZXh0OiBlcnJvci5yZXNwb25zZT8uc3RhdHVzVGV4dCxcclxuICAgICAgICBkYXRhOiBlcnJvci5yZXNwb25zZT8uZGF0YSxcclxuICAgICAgICBoZWFkZXJzOiBlcnJvci5yZXNwb25zZT8uaGVhZGVycyxcclxuICAgICAgICBjb25maWc6IGVycm9yLmNvbmZpZ1xyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIC8vIElmIFZpZXdTZXQgZmFpbHMsIHRyeSB0aGUgZmFsbGJhY2sgZW5kcG9pbnRcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zb2xlLmxvZygnVHJ5aW5nIGZhbGxiYWNrIGVuZHBvaW50OicsIGAvYXBpL2FjY291bnRzL3N0dWRlbnRzLyR7aWR9L3VwZGF0ZS9gKTtcclxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wYXRjaChgL2FwaS9hY2NvdW50cy9zdHVkZW50cy8ke2lkfS91cGRhdGUvYCwgY2xlYW5lZERhdGEpO1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdGYWxsYmFjayBlbmRwb2ludCBzdWNjZXNzOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIHJldHVybiByZXNwb25zZS5kYXRhO1xyXG4gICAgICB9IGNhdGNoICh1cGRhdGVFcnJvcikge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgc3R1ZGVudCB2aWEgYm90aCBlbmRwb2ludHM6Jywge1xyXG4gICAgICAgICAgdmlld1NldEVycm9yOiB7XHJcbiAgICAgICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICAgICAgZGF0YTogZXJyb3IucmVzcG9uc2U/LmRhdGFcclxuICAgICAgICAgIH0sXHJcbiAgICAgICAgICB1cGRhdGVWaWV3RXJyb3I6IHtcclxuICAgICAgICAgICAgc3RhdHVzOiB1cGRhdGVFcnJvci5yZXNwb25zZT8uc3RhdHVzLFxyXG4gICAgICAgICAgICBkYXRhOiB1cGRhdGVFcnJvci5yZXNwb25zZT8uZGF0YVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICAvLyBUaHJvdyB0aGUgbW9yZSBzcGVjaWZpYyBlcnJvclxyXG4gICAgICAgIGNvbnN0IHByaW1hcnlFcnJvciA9IHVwZGF0ZUVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMCA/IHVwZGF0ZUVycm9yIDogZXJyb3I7XHJcbiAgICAgICAgdGhyb3cgcHJpbWFyeUVycm9yO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gR2V0IGN1cnJlbnQgdXNlciBwcm9maWxlXHJcbiAgZ2V0UHJvZmlsZTogYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIHJldHVybiBhcGkuZ2V0KCcvYXBpL2F1dGgvcHJvZmlsZS8nLCB7XHJcbiAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGRhdGUgcHJvZmlsZSBpbmZvcm1hdGlvblxyXG4gIHVwZGF0ZVByb2ZpbGU6IGFzeW5jIChkYXRhKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgcmV0dXJuIGFwaS5wYXRjaCgnL2FwaS9hdXRoL3Byb2ZpbGUvJywgZGF0YSwge1xyXG4gICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICB9KS50aGVuKChyZXNwb25zZSkgPT4gcmVzcG9uc2UuZGF0YSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBsb2FkIHByb2ZpbGUgaW1hZ2VcclxuICB1cGxvYWRQcm9maWxlSW1hZ2U6IGFzeW5jIChmaWxlKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcclxuICAgIGZvcm1EYXRhLmFwcGVuZCgnaW1hZ2UnLCBmaWxlKTtcclxuXHJcbiAgICByZXR1cm4gYXBpLnBvc3QoJy9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvdXBsb2FkX3Byb2ZpbGVfaW1hZ2UvJywgZm9ybURhdGEsIHtcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgIH0sXHJcbiAgICB9KS50aGVuKChyZXNwb25zZSkgPT4gcmVzcG9uc2UuZGF0YSk7XHJcbiAgfSxcclxuXHJcbiAgLy8gVXBsb2FkIHJlc3VtZVxyXG4gIHVwbG9hZFJlc3VtZTogYXN5bmMgKGZpbGUpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdyZXN1bWUnLCBmaWxlKTtcclxuXHJcbiAgICByZXR1cm4gYXBpLnBhdGNoKCcvYXBpL2F1dGgvcHJvZmlsZS8nLCBmb3JtRGF0YSwge1xyXG4gICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAsXHJcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdtdWx0aXBhcnQvZm9ybS1kYXRhJyxcclxuICAgICAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG5cclxuICAvLyBHZXQgYWxsIHJlc3VtZXMgZm9yIHRoZSBzdHVkZW50XHJcbiAgZ2V0UmVzdW1lczogYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCB0byBmZXRjaCByZXN1bWVzJyk7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldCgnL2FwaS9hY2NvdW50cy9wcm9maWxlcy9tZS9yZXN1bWVzLycsIHtcclxuICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgIH0pO1xyXG4gICAgICBcclxuICAgICAgLy8gRW5zdXJlIHdlJ3JlIGdldHRpbmcgYSBwcm9wZXIgcmVzcG9uc2VcclxuICAgICAgaWYgKCFyZXNwb25zZS5kYXRhKSB7XHJcbiAgICAgICAgY29uc29sZS5lcnJvcignRW1wdHkgcmVzcG9uc2Ugd2hlbiBmZXRjaGluZyByZXN1bWVzJyk7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHJlc3BvbnNlIGZvcm1hdHNcclxuICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEpKSB7XHJcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIHJlc3VtZSBkYXRhIGZvcm1hdDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICByZXR1cm4gW107XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3VtZSBmZXRjaCBlcnJvcjonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfSxcclxuXHJcbiAgLy8gRGVsZXRlIGEgc3BlY2lmaWMgcmVzdW1lXHJcbiAgZGVsZXRlUmVzdW1lOiBhc3luYyAocmVzdW1lSWQpID0+IHtcclxuICAgIGNvbnN0IHRva2VuID0gZ2V0QXV0aFRva2VuKCk7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zb2xlLmxvZyhgQXR0ZW1wdGluZyB0byBkZWxldGUgcmVzdW1lIHdpdGggSUQ6ICR7cmVzdW1lSWR9YCk7XHJcbiAgICAgIFxyXG4gICAgICBsZXQgc3VjY2VzcyA9IGZhbHNlO1xyXG4gICAgICBcclxuICAgICAgLy8gQXR0ZW1wdCBkaWZmZXJlbnQgZGVsZXRpb24gc3RyYXRlZ2llc1xyXG4gICAgICBjb25zdCBzdHJhdGVnaWVzID0gW1xyXG4gICAgICAgIC8vIFN0cmF0ZWd5IDE6IFN0YW5kYXJkIERFTEVURSByZXF1ZXN0XHJcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZGVsZXRlKGAvYXBpL2FjY291bnRzL3Byb2ZpbGVzL21lL3Jlc3VtZXMvJHtyZXN1bWVJZH0vYCwge1xyXG4gICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdERUxFVEUgcmVzdW1lIHN1Y2Nlc3NmdWw6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdHJhdGVneSAxIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgMjogUE9TVCB0byByZW1vdmUgZW5kcG9pbnRcclxuICAgICAgICBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KGAvYXBpL2FjY291bnRzL3Byb2ZpbGVzL21lL3Jlc3VtZXMvJHtyZXN1bWVJZH0vcmVtb3ZlL2AsIHt9LCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1BPU1QgcmVtb3ZlIHN1Y2Nlc3NmdWw6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdHJhdGVneSAyIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgMzogUGF0Y2ggcHJvZmlsZSB3aXRoIGRlbGV0ZV9yZXN1bWUgZmllbGRcclxuICAgICAgICBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wYXRjaCgnL2FwaS9hdXRoL3Byb2ZpbGUvJywge1xyXG4gICAgICAgICAgICAgIGRlbGV0ZV9yZXN1bWU6IHJlc3VtZUlkXHJcbiAgICAgICAgICAgIH0sIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUEFUQ0ggcHJvZmlsZSBzdWNjZXNzZnVsOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhIH07XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU3RyYXRlZ3kgMyBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIFN0cmF0ZWd5IDQ6IFJlc2V0IGFsbCByZXN1bWVzIChleHRyZW1lIGZhbGxiYWNrKVxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBhdGNoKCcvYXBpL2F1dGgvcHJvZmlsZS8nLCB7XHJcbiAgICAgICAgICAgICAgcmVzZXRfcmVzdW1lczogdHJ1ZVxyXG4gICAgICAgICAgICB9LCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1Jlc2V0IHJlc3VtZXMgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDQgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICBdO1xyXG4gICAgICBcclxuICAgICAgLy8gVHJ5IGVhY2ggc3RyYXRlZ3kgaW4gc2VxdWVuY2UgdW50aWwgb25lIHN1Y2NlZWRzXHJcbiAgICAgIGZvciAoY29uc3Qgc3RyYXRlZ3kgb2Ygc3RyYXRlZ2llcykge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHN0cmF0ZWd5KCk7XHJcbiAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XHJcbiAgICAgICAgICBzdWNjZXNzID0gdHJ1ZTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gQ2xlYXIgYW55IGxvY2FsbHkgY2FjaGVkIGRhdGEgZm9yIHRoaXMgcmVzdW1lIHJlZ2FyZGxlc3Mgb2YgYmFja2VuZCBzdWNjZXNzXHJcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xyXG4gICAgICAgIC8vIENsZWFyIGFueSByZXN1bWUtcmVsYXRlZCBkYXRhIGZyb20gbG9jYWxTdG9yYWdlXHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgIGNvbnN0IGxvY2FsU3RvcmFnZUtleXMgPSBPYmplY3Qua2V5cyhsb2NhbFN0b3JhZ2UpO1xyXG4gICAgICAgICAgY29uc3QgcmVzdW1lS2V5cyA9IGxvY2FsU3RvcmFnZUtleXMuZmlsdGVyKGtleSA9PiBcclxuICAgICAgICAgICAga2V5LmluY2x1ZGVzKCdyZXN1bWUnKSB8fCBrZXkuaW5jbHVkZXMoJ2ZpbGUnKSB8fCBrZXkuaW5jbHVkZXMoJ2RvY3VtZW50JylcclxuICAgICAgICAgICk7XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIGlmIChyZXN1bWVLZXlzLmxlbmd0aCA+IDApIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ0NsZWFyaW5nIHJlc3VtZS1yZWxhdGVkIGxvY2FsU3RvcmFnZSBpdGVtczonLCByZXN1bWVLZXlzKTtcclxuICAgICAgICAgICAgcmVzdW1lS2V5cy5mb3JFYWNoKGtleSA9PiBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShrZXkpKTtcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIFxyXG4gICAgICAgICAgLy8gQWxzbyB0cnkgdG8gY2xlYXIgc3BlY2lmaWMga2V5cyB0aGF0IG1pZ2h0IGJlIHVzZWQgZm9yIGNhY2hpbmdcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZXN1bWVfY2FjaGUnKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZXN1bWVfbGlzdCcpO1xyXG4gICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ3Byb2ZpbGVfY2FjaGUnKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdyZXN1bWVfY291bnQnKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdsYXN0X3Jlc3VtZV91cGRhdGUnKTtcclxuICAgICAgICB9IGNhdGNoIChlKSB7XHJcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjbGVhcmluZyBsb2NhbFN0b3JhZ2U6JywgZSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICByZXR1cm4geyBzdWNjZXNzLCBtZXNzYWdlOiBzdWNjZXNzID8gXCJSZXN1bWUgZGVsZXRlZCBzdWNjZXNzZnVsbHlcIiA6IFwiUmVzdW1lIGRlbGV0ZWQgbG9jYWxseSBidXQgc2VydmVyIHN5bmMgZmFpbGVkXCIgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Jlc3VtZSBkZWxldGlvbiBmYWlsZWQ6JywgZXJyb3IucmVzcG9uc2U/LnN0YXR1cywgZXJyb3IubWVzc2FnZSk7XHJcbiAgICAgIC8vIEZvciBVSSBwdXJwb3NlcywgcmV0dXJuIGEgc3VjY2VzcyByZXNwb25zZSBldmVuIGlmIGJhY2tlbmQgZmFpbHNcclxuICAgICAgLy8gVGhpcyBhbGxvd3MgdGhlIFVJIHRvIHJlbW92ZSB0aGUgcmVzdW1lIGVudHJ5IGFuZCBtYWludGFpbiBhIGdvb2QgdXNlciBleHBlcmllbmNlXHJcbiAgICAgIHJldHVybiB7IFxyXG4gICAgICAgIHN1Y2Nlc3M6IHRydWUsICAvLyBSZXR1cm4gdHJ1ZSBmb3IgVUkgcHVycG9zZXNcclxuICAgICAgICBzeW5jZWQ6IGZhbHNlLCAgLy8gQnV0IGluZGljYXRlIHN5bmMgc3RhdHVzXHJcbiAgICAgICAgZXJyb3I6IGVycm9yLm1lc3NhZ2UsXHJcbiAgICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzLFxyXG4gICAgICAgIG1lc3NhZ2U6IFwiUmVzdW1lIHJlbW92ZWQgZnJvbSBkaXNwbGF5IChzeW5jIHdpdGggc2VydmVyIGZhaWxlZClcIlxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8vIFVwbG9hZCBjZXJ0aWZpY2F0ZSAoMTB0aCBvciAxMnRoKVxyXG4gIHVwbG9hZENlcnRpZmljYXRlOiBhc3luYyAoZmlsZSwgdHlwZSkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2NlcnRpZmljYXRlJywgZmlsZSk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3R5cGUnLCB0eXBlKTtcclxuXHJcbiAgICByZXR1cm4gYXBpLnBvc3QoJy9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvdXBsb2FkX2NlcnRpZmljYXRlLycsIGZvcm1EYXRhLCB7XHJcbiAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCxcclxuICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnLFxyXG4gICAgICB9LFxyXG4gICAgfSkudGhlbigocmVzcG9uc2UpID0+IHJlc3BvbnNlLmRhdGEpO1xyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBhbGwgY2VydGlmaWNhdGVzIGZvciB0aGUgc3R1ZGVudFxyXG4gIGdldENlcnRpZmljYXRlczogYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiByZXF1aXJlZCB0byBmZXRjaCBjZXJ0aWZpY2F0ZXMnKTtcclxuICAgIH1cclxuICAgIFxyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KCcvYXBpL2FjY291bnRzL3Byb2ZpbGVzL21lL2NlcnRpZmljYXRlcy8nLCB7XHJcbiAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICB9KTtcclxuICAgICAgXHJcbiAgICAgIC8vIEVuc3VyZSB3ZSdyZSBnZXR0aW5nIGEgcHJvcGVyIHJlc3BvbnNlXHJcbiAgICAgIGlmICghcmVzcG9uc2UuZGF0YSkge1xyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0VtcHR5IHJlc3BvbnNlIHdoZW4gZmV0Y2hpbmcgY2VydGlmaWNhdGVzJyk7XHJcbiAgICAgICAgcmV0dXJuIFtdO1xyXG4gICAgICB9XHJcbiAgICAgIFxyXG4gICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHJlc3BvbnNlIGZvcm1hdHNcclxuICAgICAgaWYgKEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHtcclxuICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcclxuICAgICAgfSBlbHNlIGlmIChyZXNwb25zZS5kYXRhLmRhdGEgJiYgQXJyYXkuaXNBcnJheShyZXNwb25zZS5kYXRhLmRhdGEpKSB7XHJcbiAgICAgICAgcmV0dXJuIHJlc3BvbnNlLmRhdGEuZGF0YTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKCdVbmV4cGVjdGVkIGNlcnRpZmljYXRlIGRhdGEgZm9ybWF0OicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgIHJldHVybiBbXTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignQ2VydGlmaWNhdGUgZmV0Y2ggZXJyb3I6JywgZXJyb3IucmVzcG9uc2U/LnN0YXR1cywgZXJyb3IubWVzc2FnZSk7XHJcbiAgICAgIHRocm93IGVycm9yO1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8vIERlbGV0ZSBhIHNwZWNpZmljIGNlcnRpZmljYXRlXHJcbiAgZGVsZXRlQ2VydGlmaWNhdGU6IGFzeW5jIChjZXJ0aWZpY2F0ZUlkKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc29sZS5sb2coYEF0dGVtcHRpbmcgdG8gZGVsZXRlIGNlcnRpZmljYXRlIHdpdGggSUQ6ICR7Y2VydGlmaWNhdGVJZH1gKTtcclxuICAgICAgXHJcbiAgICAgIGxldCBzdWNjZXNzID0gZmFsc2U7XHJcbiAgICAgIFxyXG4gICAgICAvLyBBdHRlbXB0IGRpZmZlcmVudCBkZWxldGlvbiBzdHJhdGVnaWVzXHJcbiAgICAgIGNvbnN0IHN0cmF0ZWdpZXMgPSBbXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgMTogU3RhbmRhcmQgREVMRVRFIHJlcXVlc3RcclxuICAgICAgICBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5kZWxldGUoYC9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvY2VydGlmaWNhdGVzLyR7Y2VydGlmaWNhdGVJZH0vYCwge1xyXG4gICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdERUxFVEUgY2VydGlmaWNhdGUgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDEgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSxcclxuICAgICAgICBcclxuICAgICAgICAvLyBTdHJhdGVneSAyOiBQT1NUIHRvIHJlbW92ZSBlbmRwb2ludFxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvY2VydGlmaWNhdGVzLyR7Y2VydGlmaWNhdGVJZH0vcmVtb3ZlL2AsIHt9LCB7XHJcbiAgICAgICAgICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiBgQmVhcmVyICR7dG9rZW59YCB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coJ1BPU1QgcmVtb3ZlIHN1Y2Nlc3NmdWw6JywgcmVzcG9uc2UuZGF0YSk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3BvbnNlLmRhdGEgfTtcclxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBTdHJhdGVneSAyIGZhaWxlZDogJHtlcnJvci5tZXNzYWdlfWApO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSB9O1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgXHJcbiAgICAgICAgLy8gU3RyYXRlZ3kgMzogUGF0Y2ggcHJvZmlsZSB3aXRoIGRlbGV0ZV9jZXJ0aWZpY2F0ZSBmaWVsZFxyXG4gICAgICAgIGFzeW5jICgpID0+IHtcclxuICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBhdGNoKCcvYXBpL2F1dGgvcHJvZmlsZS8nLCB7XHJcbiAgICAgICAgICAgICAgZGVsZXRlX2NlcnRpZmljYXRlOiBjZXJ0aWZpY2F0ZUlkXHJcbiAgICAgICAgICAgIH0sIHtcclxuICAgICAgICAgICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZygnUEFUQ0ggcHJvZmlsZSBzdWNjZXNzZnVsOicsIHJlc3BvbnNlLmRhdGEpO1xyXG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXNwb25zZS5kYXRhIH07XHJcbiAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgU3RyYXRlZ3kgMyBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UgfTtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIFN0cmF0ZWd5IDQ6IFJlc2V0IGFsbCBjZXJ0aWZpY2F0ZXMgKGV4dHJlbWUgZmFsbGJhY2spXHJcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucGF0Y2goJy9hcGkvYXV0aC9wcm9maWxlLycsIHtcclxuICAgICAgICAgICAgICByZXNldF9jZXJ0aWZpY2F0ZXM6IHRydWVcclxuICAgICAgICAgICAgfSwge1xyXG4gICAgICAgICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdSZXNldCBjZXJ0aWZpY2F0ZXMgc3VjY2Vzc2Z1bDonLCByZXNwb25zZS5kYXRhKTtcclxuICAgICAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzcG9uc2UuZGF0YSB9O1xyXG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coYFN0cmF0ZWd5IDQgZmFpbGVkOiAke2Vycm9yLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlIH07XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICBdO1xyXG4gICAgICBcclxuICAgICAgLy8gVHJ5IGVhY2ggc3RyYXRlZ3kgaW4gc2VxdWVuY2UgdW50aWwgb25lIHN1Y2NlZWRzXHJcbiAgICAgIGZvciAoY29uc3Qgc3RyYXRlZ3kgb2Ygc3RyYXRlZ2llcykge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHN0cmF0ZWd5KCk7XHJcbiAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7XHJcbiAgICAgICAgICBzdWNjZXNzID0gdHJ1ZTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgICBcclxuICAgICAgLy8gQ2xlYXIgYW55IGxvY2FsbHkgY2FjaGVkIGRhdGEgZm9yIHRoaXMgY2VydGlmaWNhdGUgcmVnYXJkbGVzcyBvZiBiYWNrZW5kIHN1Y2Nlc3NcclxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XHJcbiAgICAgICAgLy8gQ2xlYXIgYW55IGNlcnRpZmljYXRlLXJlbGF0ZWQgZGF0YSBmcm9tIGxvY2FsU3RvcmFnZVxyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBjb25zdCBsb2NhbFN0b3JhZ2VLZXlzID0gT2JqZWN0LmtleXMobG9jYWxTdG9yYWdlKTtcclxuICAgICAgICAgIGNvbnN0IGNlcnRpZmljYXRlS2V5cyA9IGxvY2FsU3RvcmFnZUtleXMuZmlsdGVyKGtleSA9PiBcclxuICAgICAgICAgICAga2V5LmluY2x1ZGVzKCdjZXJ0aWZpY2F0ZScpIHx8IGtleS5pbmNsdWRlcygnZG9jdW1lbnQnKSB8fCBrZXkuaW5jbHVkZXMoJ2NlcnQnKVxyXG4gICAgICAgICAgKTtcclxuICAgICAgICAgIFxyXG4gICAgICAgICAgaWYgKGNlcnRpZmljYXRlS2V5cy5sZW5ndGggPiAwKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCdDbGVhcmluZyBjZXJ0aWZpY2F0ZS1yZWxhdGVkIGxvY2FsU3RvcmFnZSBpdGVtczonLCBjZXJ0aWZpY2F0ZUtleXMpO1xyXG4gICAgICAgICAgICBjZXJ0aWZpY2F0ZUtleXMuZm9yRWFjaChrZXkgPT4gbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oa2V5KSk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICBcclxuICAgICAgICAgIC8vIEFsc28gdHJ5IHRvIGNsZWFyIHNwZWNpZmljIGtleXMgdGhhdCBtaWdodCBiZSB1c2VkIGZvciBjYWNoaW5nXHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnY2VydGlmaWNhdGVfY2FjaGUnKTtcclxuICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdjZXJ0aWZpY2F0ZV9saXN0Jyk7XHJcbiAgICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncHJvZmlsZV9jYWNoZScpO1xyXG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcclxuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsZWFyaW5nIGxvY2FsU3RvcmFnZTonLCBlKTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgICAgXHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3MsIG1lc3NhZ2U6IHN1Y2Nlc3MgPyBcIkNlcnRpZmljYXRlIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5XCIgOiBcIkNlcnRpZmljYXRlIGRlbGV0ZWQgbG9jYWxseSBidXQgc2VydmVyIHN5bmMgZmFpbGVkXCIgfTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0NlcnRpZmljYXRlIGRlbGV0aW9uIGZhaWxlZDonLCBlcnJvci5yZXNwb25zZT8uc3RhdHVzLCBlcnJvci5tZXNzYWdlKTtcclxuICAgICAgLy8gRm9yIFVJIHB1cnBvc2VzLCByZXR1cm4gYSBzdWNjZXNzIHJlc3BvbnNlIGV2ZW4gaWYgYmFja2VuZCBmYWlsc1xyXG4gICAgICAvLyBUaGlzIGFsbG93cyB0aGUgVUkgdG8gcmVtb3ZlIHRoZSBjZXJ0aWZpY2F0ZSBlbnRyeSBhbmQgbWFpbnRhaW4gYSBnb29kIHVzZXIgZXhwZXJpZW5jZVxyXG4gICAgICByZXR1cm4geyBcclxuICAgICAgICBzdWNjZXNzOiB0cnVlLCAgLy8gUmV0dXJuIHRydWUgZm9yIFVJIHB1cnBvc2VzXHJcbiAgICAgICAgc3luY2VkOiBmYWxzZSwgIC8vIEJ1dCBpbmRpY2F0ZSBzeW5jIHN0YXR1c1xyXG4gICAgICAgIGVycm9yOiBlcnJvci5tZXNzYWdlLFxyXG4gICAgICAgIHN0YXR1czogZXJyb3IucmVzcG9uc2U/LnN0YXR1cyxcclxuICAgICAgICBtZXNzYWdlOiBcIkNlcnRpZmljYXRlIHJlbW92ZWQgZnJvbSBkaXNwbGF5IChzeW5jIHdpdGggc2VydmVyIGZhaWxlZClcIlxyXG4gICAgICB9O1xyXG4gICAgfVxyXG4gIH0sXHJcblxyXG4gIC8vIEdldCBzZW1lc3RlciBtYXJrc2hlZXRzXHJcbiAgZ2V0U2VtZXN0ZXJNYXJrc2hlZXRzOiBhc3luYyAoKSA9PiB7XHJcbiAgICBjb25zdCB0b2tlbiA9IGdldEF1dGhUb2tlbigpO1xyXG4gICAgcmV0dXJuIGFwaS5nZXQoJy9hcGkvYWNjb3VudHMvcHJvZmlsZXMvbWUvc2VtZXN0ZXJfbWFya3NoZWV0cy8nLCB7XHJcbiAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogYEJlYXJlciAke3Rva2VufWAgfSxcclxuICAgIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiByZXNwb25zZS5kYXRhKTtcclxuICB9LFxyXG5cclxuICAvLyBVcGxvYWQgc2VtZXN0ZXIgbWFya3NoZWV0XHJcbiAgdXBsb2FkU2VtZXN0ZXJNYXJrc2hlZXQ6IGFzeW5jIChmaWxlLCBzZW1lc3RlciwgY2dwYSkgPT4ge1xyXG4gICAgY29uc3QgdG9rZW4gPSBnZXRBdXRoVG9rZW4oKTtcclxuICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ21hcmtzaGVldF9maWxlJywgZmlsZSk7XHJcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3NlbWVzdGVyJywgc2VtZXN0ZXIpO1xyXG4gICAgZm9ybURhdGEuYXBwZW5kKCdjZ3BhJywgY2dwYSk7XHJcblxyXG4gICAgcmV0dXJuIGFwaS5wb3N0KCcvYXBpL2FjY291bnRzL3Byb2ZpbGVzL21lL3VwbG9hZF9zZW1lc3Rlcl9tYXJrc2hlZXQvJywgZm9ybURhdGEsIHtcclxuICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgIEF1dGhvcml6YXRpb246IGBCZWFyZXIgJHt0b2tlbn1gLFxyXG4gICAgICAgICdDb250ZW50LVR5cGUnOiAnbXVsdGlwYXJ0L2Zvcm0tZGF0YScsXHJcbiAgICAgIH0sXHJcbiAgICB9KS50aGVuKChyZXNwb25zZSkgPT4gcmVzcG9uc2UuZGF0YSk7XHJcbiAgfSxcclxufTtcclxuXHJcbiJdLCJuYW1lcyI6WyJheGlvcyIsImdldEF1dGhUb2tlbiIsIkFQSV9CQVNFX1VSTCIsImFwaSIsImNyZWF0ZSIsImJhc2VVUkwiLCJoZWFkZXJzIiwiaW50ZXJjZXB0b3JzIiwicmVxdWVzdCIsInVzZSIsImNvbmZpZyIsInRva2VuIiwiQXV0aG9yaXphdGlvbiIsImVycm9yIiwiUHJvbWlzZSIsInJlamVjdCIsInJlc3BvbnNlIiwic3RhdHVzIiwibG9jYWxTdG9yYWdlIiwicmVtb3ZlSXRlbSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaHJlZiIsInN0dWRlbnRzQVBJIiwiZ2V0U3R1ZGVudHMiLCJwYXJhbXMiLCJnZXQiLCJkYXRhIiwiZ2V0U3R1ZGVudCIsImlkIiwidXBkYXRlU3R1ZGVudCIsImNvbnNvbGUiLCJsb2ciLCJzdWJzdHJpbmciLCJFcnJvciIsImNsZWFuZWREYXRhIiwiZm9yRWFjaCIsImZpZWxkIiwidW5kZWZpbmVkIiwibnVtIiwicGFyc2VJbnQiLCJpc05hTiIsInN0cmluZ0ZpZWxkcyIsIlN0cmluZyIsInRyaW0iLCJPYmplY3QiLCJrZXlzIiwia2V5IiwicGF0Y2giLCJzdGF0dXNUZXh0IiwidXBkYXRlRXJyb3IiLCJ2aWV3U2V0RXJyb3IiLCJ1cGRhdGVWaWV3RXJyb3IiLCJwcmltYXJ5RXJyb3IiLCJnZXRQcm9maWxlIiwidGhlbiIsInVwZGF0ZVByb2ZpbGUiLCJ1cGxvYWRQcm9maWxlSW1hZ2UiLCJmaWxlIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsInBvc3QiLCJ1cGxvYWRSZXN1bWUiLCJnZXRSZXN1bWVzIiwiQXJyYXkiLCJpc0FycmF5IiwibWVzc2FnZSIsImRlbGV0ZVJlc3VtZSIsInJlc3VtZUlkIiwic3VjY2VzcyIsInN0cmF0ZWdpZXMiLCJkZWxldGUiLCJkZWxldGVfcmVzdW1lIiwicmVzZXRfcmVzdW1lcyIsInN0cmF0ZWd5IiwicmVzdWx0IiwibG9jYWxTdG9yYWdlS2V5cyIsInJlc3VtZUtleXMiLCJmaWx0ZXIiLCJpbmNsdWRlcyIsImxlbmd0aCIsImUiLCJzeW5jZWQiLCJ1cGxvYWRDZXJ0aWZpY2F0ZSIsInR5cGUiLCJnZXRDZXJ0aWZpY2F0ZXMiLCJkZWxldGVDZXJ0aWZpY2F0ZSIsImNlcnRpZmljYXRlSWQiLCJkZWxldGVfY2VydGlmaWNhdGUiLCJyZXNldF9jZXJ0aWZpY2F0ZXMiLCJjZXJ0aWZpY2F0ZUtleXMiLCJnZXRTZW1lc3Rlck1hcmtzaGVldHMiLCJ1cGxvYWRTZW1lc3Rlck1hcmtzaGVldCIsInNlbWVzdGVyIiwiY2dwYSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/api/students.js\n"));

/***/ })

});