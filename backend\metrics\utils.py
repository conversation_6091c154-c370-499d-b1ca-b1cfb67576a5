from django.db.models import Count, Q, F
from django.utils import timezone
from datetime import timedelta
import hashlib
import json

from .models import MetricsCache, PaginatedDataCache
from companies.models import Company
from accounts.models import StudentProfile
from jobs.models import JobPosting, JobApplication


def calculate_dashboard_stats():
    """
    Calculate dashboard statistics
    """
    stats = {
        'total_jobs': JobPosting.objects.filter(is_active=True).count(),
        'total_applications': JobApplication.objects.count(),
        'total_students': StudentProfile.objects.count(),
        'total_companies': Company.objects.count(),
        'active_jobs': JobPosting.objects.filter(is_active=True, is_published=True).count(),
        'pending_applications': JobApplication.objects.filter(status='APPLIED').count(),
        'hiring_companies': Company.objects.filter(
            job_postings__is_active=True
        ).distinct().count(),
        'placement_rate': calculate_placement_rate(),
        'last_updated': timezone.now().isoformat()
    }
    
    return stats


def calculate_company_stats():
    """
    Calculate company-related statistics
    """
    stats = {
        'total': Company.objects.count(),
        'tier1': Company.objects.filter(tier='Tier 1').count(),
        'tier2': Company.objects.filter(tier='Tier 2').count(),
        'tier3': Company.objects.filter(tier='Tier 3').count(),
        'campus_recruiting': Company.objects.filter(campus_recruiting=True).count(),
        'with_active_jobs': Company.objects.filter(
            job_postings__is_active=True
        ).distinct().count(),
        'tier_distribution': list(Company.objects.values('tier').annotate(
            count=Count('id')
        )),
        'industry_distribution': list(Company.objects.values('industry').annotate(
            count=Count('id')
        ).order_by('-count')[:10]),
        'last_updated': timezone.now().isoformat()
    }
    
    return stats


def calculate_student_stats():
    """
    Calculate student-related statistics with enhanced metrics
    """
    current_year = timezone.now().year

    stats = {
        'total': StudentProfile.objects.count(),
        'by_year': list(StudentProfile.objects.values('passout_year').annotate(
            count=Count('id')
        ).order_by('passout_year')),
        'by_branch': list(StudentProfile.objects.values('branch').annotate(
            count=Count('id')
        ).order_by('-count')[:10]),
        'current_year_students': StudentProfile.objects.filter(
            passout_year=current_year
        ).count(),
        'graduated_students': StudentProfile.objects.filter(
            passout_year__lt=current_year
        ).count(),
        'with_applications': StudentProfile.objects.filter(
            user__job_applications__isnull=False
        ).distinct().count(),
        'average_gpa': StudentProfile.objects.aggregate(
            avg_gpa=models.Avg('gpa')
        )['avg_gpa'] or 0,
        'gpa_distribution': list(StudentProfile.objects.extra(
            select={'gpa_range': 'CASE WHEN gpa >= 9.0 THEN "9.0+" WHEN gpa >= 8.0 THEN "8.0-8.9" WHEN gpa >= 7.0 THEN "7.0-7.9" WHEN gpa >= 6.0 THEN "6.0-6.9" ELSE "Below 6.0" END'}
        ).values('gpa_range').annotate(count=Count('id')).order_by('-count')),
        'placement_ready': StudentProfile.objects.filter(
            passout_year=current_year,
            gpa__gte=6.0
        ).count(),
        'last_updated': timezone.now().isoformat()
    }

    return stats


def calculate_department_stats():
    """
    Calculate department-wise statistics
    """
    current_year = timezone.now().year

    departments = StudentProfile.objects.values('branch').annotate(
        total_students=Count('id'),
        current_year_students=Count('id', filter=models.Q(passout_year=current_year)),
        avg_gpa=models.Avg('gpa'),
        with_applications=Count('id', filter=models.Q(user__job_applications__isnull=False), distinct=True),
        placed_students=Count('id', filter=models.Q(user__job_applications__status='HIRED'), distinct=True)
    ).order_by('-total_students')

    stats = {
        'departments': list(departments),
        'total_departments': departments.count(),
        'last_updated': timezone.now().isoformat()
    }

    return stats


def calculate_placement_stats():
    """
    Calculate placement-related statistics
    """
    current_year = timezone.now().year

    # Get placement statistics
    total_eligible = StudentProfile.objects.filter(passout_year=current_year).count()
    total_placed = JobApplication.objects.filter(
        status='HIRED',
        applicant__student_profile__passout_year=current_year
    ).values('applicant').distinct().count()

    placement_rate = (total_placed / total_eligible * 100) if total_eligible > 0 else 0

    # Company-wise placements
    company_placements = JobApplication.objects.filter(
        status='HIRED',
        applicant__student_profile__passout_year=current_year
    ).values('job__company__name', 'job__company__tier').annotate(
        count=Count('id')
    ).order_by('-count')[:10]

    # Department-wise placement rates
    dept_placements = StudentProfile.objects.filter(
        passout_year=current_year
    ).values('branch').annotate(
        total_students=Count('id'),
        placed_students=Count('id', filter=models.Q(user__job_applications__status='HIRED'), distinct=True)
    ).order_by('-placed_students')

    for dept in dept_placements:
        dept['placement_rate'] = (dept['placed_students'] / dept['total_students'] * 100) if dept['total_students'] > 0 else 0

    stats = {
        'total_eligible': total_eligible,
        'total_placed': total_placed,
        'placement_rate': round(placement_rate, 2),
        'company_wise_placements': list(company_placements),
        'department_wise_placements': list(dept_placements),
        'last_updated': timezone.now().isoformat()
    }

    return stats


def calculate_job_stats():
    """
    Calculate job-related statistics
    """
    stats = {
        'total': JobPosting.objects.count(),
        'active': JobPosting.objects.filter(is_active=True).count(),
        'published': JobPosting.objects.filter(is_published=True).count(),
        'internships': JobPosting.objects.filter(job_type='INTERNSHIP').count(),
        'full_time': JobPosting.objects.filter(job_type='FULL_TIME').count(),
        'on_campus': JobPosting.objects.filter(on_campus=True).count(),
        'remote_eligible': JobPosting.objects.filter(remote_eligible=True).count(),
        'by_type': list(JobPosting.objects.values('job_type').annotate(
            count=Count('id')
        )),
        'by_location': list(JobPosting.objects.values('location').annotate(
            count=Count('id')
        ).order_by('-count')[:10]),
        'expiring_soon': JobPosting.objects.filter(
            application_deadline__gte=timezone.now(),
            application_deadline__lte=timezone.now() + timedelta(days=7),
            is_active=True
        ).count(),
        'last_updated': timezone.now().isoformat()
    }
    
    return stats


def calculate_application_stats():
    """
    Calculate application-related statistics
    """
    stats = {
        'total': JobApplication.objects.count(),
        'by_status': list(JobApplication.objects.values('status').annotate(
            count=Count('id')
        )),
        'recent_applications': JobApplication.objects.filter(
            applied_at__gte=timezone.now() - timedelta(days=7)
        ).count(),
        'pending_review': JobApplication.objects.filter(
            status__in=['APPLIED', 'UNDER_REVIEW']
        ).count(),
        'interviews_scheduled': JobApplication.objects.filter(
            status='SHORTLISTED'
        ).count(),
        'hired': JobApplication.objects.filter(status='HIRED').count(),
        'rejected': JobApplication.objects.filter(status='REJECTED').count(),
        'last_updated': timezone.now().isoformat()
    }
    
    return stats


def calculate_placement_rate():
    """
    Calculate overall placement rate
    """
    total_eligible = StudentProfile.objects.filter(
        passout_year__lte=timezone.now().year
    ).count()
    
    if total_eligible == 0:
        return 0.0
        
    placed = JobApplication.objects.filter(
        status='HIRED',
        applicant__student_profile__passout_year__lte=timezone.now().year
    ).values('applicant').distinct().count()
    
    return round((placed / total_eligible) * 100, 2)


def get_or_calculate_metric(metric_type, metric_key='default', force_refresh=False):
    """
    Get metric from cache or calculate if not available/expired
    """
    if not force_refresh:
        cached_data = MetricsCache.get_cached_metric(metric_type, metric_key)
        if cached_data:
            return cached_data
    
    # Calculate fresh data
    calculators = {
        'dashboard_stats': calculate_dashboard_stats,
        'company_stats': calculate_company_stats,
        'student_stats': calculate_student_stats,
        'job_stats': calculate_job_stats,
        'application_stats': calculate_application_stats,
        'department_stats': calculate_department_stats,
        'placement_stats': calculate_placement_stats,
    }
    
    calculator = calculators.get(metric_type)
    if not calculator:
        return None
    
    fresh_data = calculator()
    
    # Cache the fresh data
    MetricsCache.update_metric(metric_type, metric_key, fresh_data)
    
    return fresh_data


def generate_filter_hash(filters):
    """
    Generate a hash for filter parameters to use as cache key
    """
    # Sort the filters to ensure consistent hash
    sorted_filters = json.dumps(filters, sort_keys=True)
    return hashlib.md5(sorted_filters.encode()).hexdigest()


def invalidate_related_metrics(*data_types):
    """
    Invalidate metrics when related data changes
    """
    metric_mappings = {
        'company': ['dashboard_stats', 'company_stats'],
        'student': ['dashboard_stats', 'student_stats'],
        'job': ['dashboard_stats', 'job_stats', 'company_stats'],
        'application': ['dashboard_stats', 'application_stats', 'student_stats'],
    }
    
    for data_type in data_types:
        metrics_to_invalidate = metric_mappings.get(data_type, [])
        for metric_type in metrics_to_invalidate:
            MetricsCache.invalidate_metric(metric_type)


def invalidate_paginated_cache(*cache_types):
    """
    Invalidate paginated cache when data changes
    """
    for cache_type in cache_types:
        PaginatedDataCache.invalidate_cache(cache_type)


def get_cached_paginated_data(cache_type, filters, page, page_size, 
                              data_fetcher, force_refresh=False):
    """
    Generic function to get cached paginated data with memory optimization
    
    Args:
        cache_type: Type of cache (students_list, companies_list, etc.)
        filters: Filter parameters as dict
        page: Page number
        page_size: Items per page
        data_fetcher: Function that fetches fresh data when cache miss
        force_refresh: Force refresh cache
    
    Returns:
        Dict with data, pagination info
    """
    filter_hash = generate_filter_hash(filters)
    
    if not force_refresh:
        cached_data = PaginatedDataCache.get_cached_page(
            cache_type, filter_hash, page, page_size
        )
        if cached_data:
            return {
                'results': cached_data['data'],
                'pagination': {
                    'page': cached_data['page_number'],
                    'page_size': cached_data['page_size'],
                    'total_count': cached_data['total_count'],
                    'total_pages': (cached_data['total_count'] + page_size - 1) // page_size,
                    'has_next': page < ((cached_data['total_count'] + page_size - 1) // page_size),
                    'has_previous': page > 1,
                }
            }
    
    # Fetch fresh data - this should only fetch the current page
    fresh_data = data_fetcher(filters, page, page_size)
    
    # Cache the fresh data
    if 'data' in fresh_data and 'total_count' in fresh_data:
        PaginatedDataCache.update_cached_page(
            cache_type, filter_hash, page, page_size,
            fresh_data['data'], fresh_data['total_count']
        )
        
        return {
            'results': fresh_data['data'],
            'pagination': {
                'page': fresh_data['page'],
                'page_size': fresh_data['page_size'],
                'total_count': fresh_data['total_count'],
                'total_pages': (fresh_data['total_count'] + page_size - 1) // page_size,
                'has_next': fresh_data['page'] < ((fresh_data['total_count'] + page_size - 1) // page_size),
                'has_previous': fresh_data['page'] > 1,
            }
        }
    
    return fresh_data
