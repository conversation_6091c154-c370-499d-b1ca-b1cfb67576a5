from rest_framework import generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.core.paginator import Paginator
from django.db.models import Q

from metrics.utils import (
    get_or_calculate_metric, 
    get_cached_paginated_data,
    generate_filter_hash
)
from metrics.models import PaginatedDataCache
from companies.models import Company
from companies.serializers import CompanySerializer
from accounts.models import StudentProfile
from accounts.serializers import StudentProfileListSerializer
from jobs.models import JobPosting, JobApplication
from jobs.serializers import EnhancedJobSerializer, JobApplicationSerializer


class CachedMetricsView(APIView):
    """
    API endpoint that returns cached metrics for dashboard
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        metric_type = request.query_params.get('type', 'dashboard_stats')
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        data = get_or_calculate_metric(metric_type, force_refresh=force_refresh)
        
        if data is None:
            return Response({'error': 'Invalid metric type'}, status=400)
        
        return Response(data)


class CachedCompanyListView(generics.ListAPIView):
    """
    Cached company list with pagination
    """
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters with enhanced filtering options
        filters = {
            'search': request.query_params.get('search', '').strip(),
            'tier': request.query_params.get('tier', '').strip(),
            'industry': request.query_params.get('industry', '').strip(),
            'location': request.query_params.get('location', '').strip(),  # Added location filter
            'size': request.query_params.get('size', '').strip(),  # Added size filter
            'campus_recruiting': request.query_params.get('campus_recruiting', '').strip(),
            'min_jobs': request.query_params.get('min_jobs', '').strip(),  # Added min jobs filter
            'founded_after': request.query_params.get('founded_after', '').strip(),  # Added founded year filter
        }

        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 10)), 100)  # Limit max page size
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_companies(filters, page, page_size):
            """Fetch fresh company data with enhanced filtering and performance optimization"""
            # Use prefetch_related for better performance with related objects
            queryset = Company.objects.prefetch_related(
                'job_postings', 'followers'
            ).all()

            # Apply filters
            if filters['search']:
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(name__icontains=search_term) |
                    Q(description__icontains=search_term) |
                    Q(industry__icontains=search_term) |
                    Q(location__icontains=search_term)
                )

            if filters['tier']:
                queryset = queryset.filter(tier=filters['tier'])

            if filters['industry']:
                queryset = queryset.filter(industry__icontains=filters['industry'])

            if filters['location']:
                queryset = queryset.filter(location__icontains=filters['location'])

            if filters['size']:
                queryset = queryset.filter(size__icontains=filters['size'])

            if filters['campus_recruiting']:
                queryset = queryset.filter(
                    campus_recruiting=filters['campus_recruiting'].lower() == 'true'
                )

            if filters['min_jobs']:
                try:
                    min_jobs = int(filters['min_jobs'])
                    queryset = queryset.filter(total_active_jobs__gte=min_jobs)
                except ValueError:
                    pass

            if filters['founded_after']:
                try:
                    year = int(filters['founded_after'])
                    queryset = queryset.filter(founded__gte=str(year))
                except ValueError:
                    pass

            # Order by name for consistent pagination
            queryset = queryset.order_by('name', 'id')

            # Get total count before pagination (use count() for efficiency)
            total_count = queryset.count()

            # Apply pagination using database LIMIT/OFFSET for efficiency
            start = (page - 1) * page_size
            end = start + page_size
            companies = list(queryset[start:end])

            # Serialize data
            serializer = CompanySerializer(companies, many=True, context={'request': request})

            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'has_next': page * page_size < total_count,
                'has_previous': page > 1,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'companies_list', filters, page, page_size, 
            fetch_companies, force_refresh
        )
        
        return Response(result)


class CachedStudentListView(generics.ListAPIView):
    """
    Cached student list with pagination
    """
    serializer_class = StudentProfileListSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters with enhanced filtering options
        filters = {
            'search': request.query_params.get('search', '').strip(),
            'department': request.query_params.get('department', '').strip(),
            'branch': request.query_params.get('branch', '').strip(),  # Added branch filter
            'year': request.query_params.get('year', '').strip(),
            'passout_year': request.query_params.get('passout_year', '').strip(),  # Added passout year
            'joining_year': request.query_params.get('joining_year', '').strip(),  # Added joining year
            'cgpa_min': request.query_params.get('cgpa_min', '').strip(),
            'cgpa_max': request.query_params.get('cgpa_max', '').strip(),
            'year_range': request.query_params.get('year_range', '').strip(),  # Added year range filter
        }

        page = int(request.query_params.get('page', 1))
        page_size = min(int(request.query_params.get('page_size', 10)), 100)  # Limit max page size
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_students(filters, page, page_size):
            """Fetch fresh student data with enhanced filtering and performance optimization"""
            # Use select_related and prefetch_related for better performance
            queryset = StudentProfile.objects.select_related(
                'user', 'college'
            ).prefetch_related(
                'user__job_applications'
            ).all()

            # Apply filters
            if filters['search']:
                search_term = filters['search']
                queryset = queryset.filter(
                    Q(first_name__icontains=search_term) |
                    Q(last_name__icontains=search_term) |
                    Q(student_id__icontains=search_term) |
                    Q(contact_email__icontains=search_term) |
                    Q(user__email__icontains=search_term)
                )

            # Handle both department and branch filters
            if filters['department']:
                queryset = queryset.filter(branch__icontains=filters['department'])
            elif filters['branch']:
                queryset = queryset.filter(branch__icontains=filters['branch'])

            # Handle multiple year filters
            if filters['year']:
                try:
                    year = int(filters['year'])
                    queryset = queryset.filter(passout_year=year)
                except ValueError:
                    pass

            if filters['passout_year']:
                try:
                    year = int(filters['passout_year'])
                    queryset = queryset.filter(passout_year=year)
                except ValueError:
                    pass

            if filters['joining_year']:
                try:
                    year = int(filters['joining_year'])
                    queryset = queryset.filter(joining_year=year)
                except ValueError:
                    pass

            if filters['year_range']:
                # Handle year range like "2020-2024"
                try:
                    if '-' in filters['year_range']:
                        start_year, end_year = filters['year_range'].split('-')
                        start_year = int(start_year.strip())
                        end_year = int(end_year.strip())
                        queryset = queryset.filter(
                            joining_year__gte=start_year,
                            passout_year__lte=end_year
                        )
                except (ValueError, AttributeError):
                    pass

            if filters['cgpa_min']:
                try:
                    cgpa_min = float(filters['cgpa_min'])
                    queryset = queryset.filter(gpa__gte=cgpa_min)
                except ValueError:
                    pass

            if filters['cgpa_max']:
                try:
                    cgpa_max = float(filters['cgpa_max'])
                    queryset = queryset.filter(gpa__lte=cgpa_max)
                except ValueError:
                    pass

            # Order by name for consistent pagination
            queryset = queryset.order_by('first_name', 'last_name', 'id')

            # Get total count before pagination (use count() for efficiency)
            total_count = queryset.count()

            # Apply pagination using database LIMIT/OFFSET for efficiency
            start = (page - 1) * page_size
            end = start + page_size
            students = list(queryset[start:end])

            # Serialize data
            serializer = StudentProfileListSerializer(students, many=True, context={'request': request})

            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size,
                'has_next': page * page_size < total_count,
                'has_previous': page > 1,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'students_list', filters, page, page_size, 
            fetch_students, force_refresh
        )
        
        return Response(result)


class CachedJobListView(generics.ListAPIView):
    """
    Cached job list with pagination
    """
    serializer_class = EnhancedJobSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters
        filters = {
            'search': request.query_params.get('search', ''),
            'job_type': request.query_params.get('job_type', ''),
            'location': request.query_params.get('location', ''),
            'company': request.query_params.get('company', ''),
            'is_active': request.query_params.get('is_active', 'true'),
        }
        
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_jobs(filters, page, page_size):
            """Fetch fresh job data"""
            queryset = JobPosting.objects.select_related('company').all()
            
            # Apply filters
            if filters['search']:
                queryset = queryset.filter(
                    Q(title__icontains=filters['search']) |
                    Q(description__icontains=filters['search']) |
                    Q(company__name__icontains=filters['search'])
                )
            
            if filters['job_type']:
                queryset = queryset.filter(job_type=filters['job_type'])
                
            if filters['location']:
                queryset = queryset.filter(location__icontains=filters['location'])
                
            if filters['company']:
                queryset = queryset.filter(company__name__icontains=filters['company'])
                
            if filters['is_active'].lower() == 'true':
                queryset = queryset.filter(is_active=True)
            
            # Order by creation date
            queryset = queryset.order_by('-created_at')
            
            # Get total count before pagination
            total_count = queryset.count()
            
            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            jobs = list(queryset[start:end])
            
            # Serialize data
            serializer = EnhancedJobSerializer(jobs, many=True, context={'request': request})
            
            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'jobs_list', filters, page, page_size, 
            fetch_jobs, force_refresh
        )
        
        return Response(result)


class CacheStatusView(APIView):
    """
    View to check cache status and statistics
    """
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        from metrics.models import MetricsCache, PaginatedDataCache
        
        metrics_count = MetricsCache.objects.filter(is_valid=True).count()
        invalid_metrics = MetricsCache.objects.filter(is_valid=False).count()
        
        pagination_count = PaginatedDataCache.objects.filter(is_valid=True).count()
        invalid_pagination = PaginatedDataCache.objects.filter(is_valid=False).count()
        
        return Response({
            'metrics_cache': {
                'valid_entries': metrics_count,
                'invalid_entries': invalid_metrics,
                'total': metrics_count + invalid_metrics
            },
            'pagination_cache': {
                'valid_entries': pagination_count,
                'invalid_entries': invalid_pagination,
                'total': pagination_count + invalid_pagination
            }
        })
