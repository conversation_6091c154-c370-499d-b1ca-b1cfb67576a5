from rest_framework import generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from django.core.paginator import Paginator
from django.db.models import Q

from metrics.utils import (
    get_or_calculate_metric, 
    get_cached_paginated_data,
    generate_filter_hash
)
from metrics.models import PaginatedDataCache
from companies.models import Company
from companies.serializers import CompanySerializer
from accounts.models import StudentProfile
from accounts.serializers import StudentProfileListSerializer
from jobs.models import JobPosting, JobApplication
from jobs.serializers import EnhancedJobSerializer, JobApplicationSerializer


class CachedMetricsView(APIView):
    """
    API endpoint that returns cached metrics for dashboard
    """
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        metric_type = request.query_params.get('type', 'dashboard_stats')
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        data = get_or_calculate_metric(metric_type, force_refresh=force_refresh)
        
        if data is None:
            return Response({'error': 'Invalid metric type'}, status=400)
        
        return Response(data)


class CachedCompanyListView(generics.ListAPIView):
    """
    Cached company list with pagination
    """
    serializer_class = CompanySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters
        filters = {
            'search': request.query_params.get('search', ''),
            'tier': request.query_params.get('tier', ''),
            'industry': request.query_params.get('industry', ''),
            'campus_recruiting': request.query_params.get('campus_recruiting', ''),
        }
        
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_companies(filters, page, page_size):
            """Fetch fresh company data"""
            queryset = Company.objects.all()
            
            # Apply filters
            if filters['search']:
                queryset = queryset.filter(
                    Q(name__icontains=filters['search']) |
                    Q(description__icontains=filters['search']) |
                    Q(industry__icontains=filters['search'])
                )
            
            if filters['tier']:
                queryset = queryset.filter(tier=filters['tier'])
                
            if filters['industry']:
                queryset = queryset.filter(industry=filters['industry'])
                
            if filters['campus_recruiting']:
                queryset = queryset.filter(
                    campus_recruiting=filters['campus_recruiting'].lower() == 'true'
                )
            
            # Get total count before pagination
            total_count = queryset.count()
            
            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            companies = list(queryset[start:end])
            
            # Serialize data
            serializer = CompanySerializer(companies, many=True, context={'request': request})
            
            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'companies_list', filters, page, page_size, 
            fetch_companies, force_refresh
        )
        
        return Response(result)


class CachedStudentListView(generics.ListAPIView):
    """
    Cached student list with pagination
    """
    serializer_class = StudentProfileListSerializer
    permission_classes = [permissions.IsAdminUser]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters
        filters = {
            'search': request.query_params.get('search', ''),
            'department': request.query_params.get('department', ''),
            'year': request.query_params.get('year', ''),
            'cgpa_min': request.query_params.get('cgpa_min', ''),
            'cgpa_max': request.query_params.get('cgpa_max', ''),
        }
        
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_students(filters, page, page_size):
            """Fetch fresh student data"""
            queryset = StudentProfile.objects.select_related('user').all()
            
            # Apply filters
            if filters['search']:
                queryset = queryset.filter(
                    Q(first_name__icontains=filters['search']) |
                    Q(last_name__icontains=filters['search']) |
                    Q(student_id__icontains=filters['search']) |
                    Q(user__email__icontains=filters['search'])
                )
            
            if filters['department']:
                queryset = queryset.filter(branch=filters['department'])
                
            if filters['year']:
                queryset = queryset.filter(passout_year=filters['year'])
                
            if filters['cgpa_min']:
                try:
                    cgpa_min = float(filters['cgpa_min'])
                    queryset = queryset.filter(gpa__gte=cgpa_min)
                except ValueError:
                    pass
                    
            if filters['cgpa_max']:
                try:
                    cgpa_max = float(filters['cgpa_max'])
                    queryset = queryset.filter(gpa__lte=cgpa_max)
                except ValueError:
                    pass
            
            # Get total count before pagination
            total_count = queryset.count()
            
            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            students = list(queryset[start:end])
            
            # Serialize data
            serializer = StudentProfileListSerializer(students, many=True, context={'request': request})
            
            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'students_list', filters, page, page_size, 
            fetch_students, force_refresh
        )
        
        return Response(result)


class CachedJobListView(generics.ListAPIView):
    """
    Cached job list with pagination
    """
    serializer_class = EnhancedJobSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def list(self, request, *args, **kwargs):
        # Get filter parameters
        filters = {
            'search': request.query_params.get('search', ''),
            'job_type': request.query_params.get('job_type', ''),
            'location': request.query_params.get('location', ''),
            'company': request.query_params.get('company', ''),
            'is_active': request.query_params.get('is_active', 'true'),
        }
        
        page = int(request.query_params.get('page', 1))
        page_size = int(request.query_params.get('page_size', 10))
        force_refresh = request.query_params.get('refresh', 'false').lower() == 'true'
        
        def fetch_jobs(filters, page, page_size):
            """Fetch fresh job data"""
            queryset = JobPosting.objects.select_related('company').all()
            
            # Apply filters
            if filters['search']:
                queryset = queryset.filter(
                    Q(title__icontains=filters['search']) |
                    Q(description__icontains=filters['search']) |
                    Q(company__name__icontains=filters['search'])
                )
            
            if filters['job_type']:
                queryset = queryset.filter(job_type=filters['job_type'])
                
            if filters['location']:
                queryset = queryset.filter(location__icontains=filters['location'])
                
            if filters['company']:
                queryset = queryset.filter(company__name__icontains=filters['company'])
                
            if filters['is_active'].lower() == 'true':
                queryset = queryset.filter(is_active=True)
            
            # Order by creation date
            queryset = queryset.order_by('-created_at')
            
            # Get total count before pagination
            total_count = queryset.count()
            
            # Apply pagination
            start = (page - 1) * page_size
            end = start + page_size
            jobs = list(queryset[start:end])
            
            # Serialize data
            serializer = EnhancedJobSerializer(jobs, many=True, context={'request': request})
            
            return {
                'data': serializer.data,
                'total_count': total_count,
                'page': page,
                'page_size': page_size
            }
        
        # Get cached or fresh data
        result = get_cached_paginated_data(
            'jobs_list', filters, page, page_size, 
            fetch_jobs, force_refresh
        )
        
        return Response(result)


class CacheStatusView(APIView):
    """
    View to check cache status and statistics
    """
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        from metrics.models import MetricsCache, PaginatedDataCache
        
        metrics_count = MetricsCache.objects.filter(is_valid=True).count()
        invalid_metrics = MetricsCache.objects.filter(is_valid=False).count()
        
        pagination_count = PaginatedDataCache.objects.filter(is_valid=True).count()
        invalid_pagination = PaginatedDataCache.objects.filter(is_valid=False).count()
        
        return Response({
            'metrics_cache': {
                'valid_entries': metrics_count,
                'invalid_entries': invalid_metrics,
                'total': metrics_count + invalid_metrics
            },
            'pagination_cache': {
                'valid_entries': pagination_count,
                'invalid_entries': invalid_pagination,
                'total': pagination_count + invalid_pagination
            }
        })
