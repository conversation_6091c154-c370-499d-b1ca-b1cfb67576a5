from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'companies'

router = DefaultRouter()
router.register(r'companies', views.CompanyViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('company/<int:pk>/', views.CompanyDetailView.as_view(), name='company-detail'),
    path('companies/<int:pk>/upload-logo/', views.CompanyLogoUploadView.as_view(), name='company-logo-upload'),
    path('company-list/', views.CompanyListView.as_view(), name='company-list'),
    path('companies/optimized/', views.OptimizedCompanyListView.as_view(), name='optimized-company-list'),

    # Follower endpoints
    path('companies/<int:company_id>/followers/count/', views.CompanyFollowersCountView.as_view(), name='company-followers-count'),
    path('companies/<int:company_id>/followers/status/', views.CompanyFollowerStatusView.as_view(), name='company-follower-status'),
    path('companies/<int:company_id>/followers/', views.CompanyFollowerView.as_view(), name='company-followers'),
    path('users/<int:user_id>/following/', views.UserFollowedCompaniesView.as_view(), name='user-followed-companies'),

    # Company stats
    path('companies/stats/', views.CompanyStatsView.as_view(), name='company-stats'),
]