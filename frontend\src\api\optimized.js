/**
 * Optimized API service for server-side pagination and filtering
 * Replaces inefficient client-side data loading patterns
 */

import client from './client';

/**
 * Generic function for paginated API calls
 * @param {string} endpoint - API endpoint
 * @param {Object} params - Query parameters
 * @returns {Promise} API response
 */
export async function fetchPaginatedData(endpoint, params = {}) {
  try {
    const queryParams = new URLSearchParams();
    
    // Add pagination parameters
    if (params.page) queryParams.append('page', params.page);
    if (params.page_size) queryParams.append('page_size', params.page_size);
    
    // Add filter parameters
    Object.keys(params).forEach(key => {
      if (key !== 'page' && key !== 'page_size' && params[key]) {
        queryParams.append(key, params[key]);
      }
    });
    
    const url = `${endpoint}?${queryParams.toString()}`;
    const response = await client.get(url);
    
    return {
      success: true,
      data: response.data.results || response.data.data || response.data,
      pagination: response.data.pagination || {
        page: params.page || 1,
        page_size: params.page_size || 20,
        total_count: response.data.total_count || 0,
        total_pages: response.data.total_pages || 1,
        has_next: response.data.has_next || false,
        has_previous: response.data.has_previous || false
      },
      metadata: response.data.metadata || {}
    };
  } catch (error) {
    console.error(`Error fetching paginated data from ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Optimized student API functions
 */
export const studentsAPI = {
  /**
   * Fetch students with server-side pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise} Students data with pagination
   */
  async getStudents(params = {}) {
    return fetchPaginatedData('/api/v1/accounts/students/optimized/', params);
  },

  /**
   * Get student statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Student statistics
   */
  async getStudentStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'student_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching student stats:', error);
      throw error;
    }
  },

  /**
   * Get department statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Department statistics
   */
  async getDepartmentStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'department_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching department stats:', error);
      throw error;
    }
  },

  /**
   * Search students with optimized backend search
   * @param {string} searchTerm - Search term
   * @param {Object} filters - Additional filters
   * @param {number} page - Page number
   * @param {number} pageSize - Page size
   * @returns {Promise} Search results
   */
  async searchStudents(searchTerm, filters = {}, page = 1, pageSize = 20) {
    const params = {
      search: searchTerm,
      page,
      page_size: pageSize,
      ...filters
    };
    return this.getStudents(params);
  }
};

/**
 * Optimized company API functions
 */
export const companiesAPI = {
  /**
   * Fetch companies with server-side pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise} Companies data with pagination
   */
  async getCompanies(params = {}) {
    return fetchPaginatedData('/api/v1/companies/optimized/', params);
  },

  /**
   * Get company statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Company statistics
   */
  async getCompanyStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'company_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching company stats:', error);
      throw error;
    }
  },

  /**
   * Search companies with optimized backend search
   * @param {string} searchTerm - Search term
   * @param {Object} filters - Additional filters
   * @param {number} page - Page number
   * @param {number} pageSize - Page size
   * @returns {Promise} Search results
   */
  async searchCompanies(searchTerm, filters = {}, page = 1, pageSize = 20) {
    const params = {
      search: searchTerm,
      page,
      page_size: pageSize,
      ...filters
    };
    return this.getCompanies(params);
  }
};

/**
 * Optimized jobs API functions
 */
export const jobsAPI = {
  /**
   * Fetch jobs with server-side pagination and filtering
   * @param {Object} params - Query parameters
   * @returns {Promise} Jobs data with pagination
   */
  async getJobs(params = {}) {
    return fetchPaginatedData('/api/v1/jobs/', params);
  },

  /**
   * Get job statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Job statistics
   */
  async getJobStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'job_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching job stats:', error);
      throw error;
    }
  }
};

/**
 * Dashboard API functions
 */
export const dashboardAPI = {
  /**
   * Get dashboard statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Dashboard statistics
   */
  async getDashboardStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'dashboard_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  /**
   * Get placement statistics
   * @param {boolean} forceRefresh - Force refresh cache
   * @returns {Promise} Placement statistics
   */
  async getPlacementStats(forceRefresh = false) {
    try {
      const params = forceRefresh ? { refresh: 'true' } : {};
      const response = await client.get('/api/v1/metrics/cached/', {
        params: { type: 'placement_stats', ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching placement stats:', error);
      throw error;
    }
  }
};

/**
 * Utility functions for pagination
 */
export const paginationUtils = {
  /**
   * Calculate pagination info
   * @param {number} currentPage - Current page
   * @param {number} totalPages - Total pages
   * @param {number} totalCount - Total items
   * @param {number} pageSize - Items per page
   * @returns {Object} Pagination info
   */
  calculatePaginationInfo(currentPage, totalPages, totalCount, pageSize) {
    return {
      currentPage,
      totalPages,
      totalCount,
      pageSize,
      startIndex: (currentPage - 1) * pageSize + 1,
      endIndex: Math.min(currentPage * pageSize, totalCount),
      hasNext: currentPage < totalPages,
      hasPrevious: currentPage > 1
    };
  },

  /**
   * Generate page numbers for pagination component
   * @param {number} currentPage - Current page
   * @param {number} totalPages - Total pages
   * @param {number} maxVisible - Maximum visible page numbers
   * @returns {Array} Array of page numbers
   */
  generatePageNumbers(currentPage, totalPages, maxVisible = 5) {
    const pages = [];
    const half = Math.floor(maxVisible / 2);
    
    let start = Math.max(1, currentPage - half);
    let end = Math.min(totalPages, start + maxVisible - 1);
    
    // Adjust start if we're near the end
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    return pages;
  }
};

/**
 * Cache management utilities
 */
export const cacheUtils = {
  /**
   * Clear all cached data
   */
  clearAllCache() {
    // Clear localStorage cache
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {
        localStorage.removeItem(key);
      }
    });
    
    // Clear sessionStorage cache
    const sessionKeys = Object.keys(sessionStorage);
    sessionKeys.forEach(key => {
      if (key.startsWith('cache_') || key.includes('_data') || key.includes('_timestamp')) {
        sessionStorage.removeItem(key);
      }
    });
  },

  /**
   * Check if cached data is still valid
   * @param {string} key - Cache key
   * @param {number} maxAge - Maximum age in milliseconds
   * @returns {boolean} Whether cache is valid
   */
  isCacheValid(key, maxAge = 5 * 60 * 1000) { // 5 minutes default
    const timestamp = localStorage.getItem(`${key}_timestamp`);
    if (!timestamp) return false;
    
    const age = Date.now() - parseInt(timestamp);
    return age < maxAge;
  }
};
